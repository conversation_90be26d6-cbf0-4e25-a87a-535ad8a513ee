<?php include_once '../header.php'; ?>

<?php
    $project_root_path = dirname(dirname(__DIR__));
    $document_root = $_SERVER['DOCUMENT_ROOT'];

    $project_root_path = str_replace('\\', '/', $project_root_path);
    $document_root = str_replace('\\', '/', $document_root);
    
    $base_path = str_replace($document_root, '', $project_root_path);

    $base_path = rtrim($base_path, '/') . '/';
?>
<script>
    window.BASE_URL = '<?php echo $base_path; ?>';
</script>

<script src="js/script-v1.js"></script>
<script src="../../assets/js/stepper.js"></script>
<script src="../../assets/js/purchase-stepper.js"></script>
<script src="../carrinho/js/script-vDev.js"></script>
<script src="js/script-pagbank.js"></script>

<div class="reserva-label-container">
	<div class="col">
		<a href="../carrinho" class="btn-outlined-custom fw-500 gap-10">
			<i class="bi bi-arrow-left"></i>
			<span>Voltar</span>
		</a>
	</div>
</div>

<div class="purchase-stepper-container">
	<div id="purchase-stepper"></div>
</div>

<div class="mt-4">
	<p class="fs-16 fw-400 green-30"> Criaremos seu cadastro para finalizar o agendamento. </p>

	<p class="fs-16 fw-400 green-30">
		O login será o CPF informado, junto com a senha definida. Para reservas futuras, basta acessar com
		esses dados.
	</p>
</div>

<form id="cadastrarCliente" method="post">
	<div class="d-flex flex-column gap-2 mt-3">
		<div class="campo-input">
			<label class="fw-600 fs-16 green-50">Nome</label>
			<div class="input-container-identificacao">
				<div class="input-frame">
					<input type="text" name="nome" class="input-identificacao nome fs-12 fw-400 green-50 bg-beige-50"
						required="required">
				</div>
			</div>
		</div>

		<div class="campo-input">
			<label class="fw-600 fs-16 green-50">Sobrenome</label>
			<div class="input-container-identificacao">
				<div class="input-frame">
					<input type="text" name="sobrenome" class="input-identificacao sobrenome fs-12 fw-400 green-50 bg-beige-50"
						required="required">
				</div>
			</div>
		</div>

		<div class="campo-input">
			<label class="fw-600 fs-16 green-50">Nacionalidade</label>
			<div class="input-container-identificacao">
				<div class="input-frame">
					<select name="estrangeiro" id="estrangeiro" class="input-identificacao estrangeiro fs-12 fw-400 green-50 bg-beige-50"
						required="required">
						<option value="0"> Brasileiro </option>
						<option value="1"> Estrangeiro </option>
					</select>
				</div>
			</div>
		</div>

		<div class="campo-input">
			<label class="fw-600 fs-16 green-50">CPF</label>
			<div class="input-container-identificacao">
				<div class="input-frame">
					<input type="text" name="cpf" class="input-identificacao cpf fs-12 fw-400 green-50 bg-beige-50"
						required="required" inputmode="numeric">
				</div>
			</div>
		</div>

		<div class="campo-input">
			<label class="fw-600 fs-16 green-50">E-mail</label>
			<div class="input-container-identificacao">
				<div class="input-frame">
					<input type="email" name="email" class="input-identificacao email fs-12 fw-400 green-50 bg-beige-50"
						required="required" autocomplete="off">
				</div>
			</div>
		</div>

		<div class="campo-input">
			<label class="fw-600 fs-16 green-50">Celular/WhatsApp</label>
			<div class="input-container-identificacao">
				<div class="input-frame">

					<div class="dropdown">
						<button class="btn btn-light rounded-end-0 border-0 btn-lg dropdown-toggle" type="button"
							id="telefone1-ddi" data-bs-toggle="dropdown" aria-expanded="false">
							+55
						</button>

						<?php include_once 'ddi1.php'; ?>
					</div>

					<input type="text" name="telefone1" class="input-identificacao telefone1 fs-12 fw-400 green-50 bg-beige-50"
						required="required" inputmode="numeric">
				</div>
			</div>
		</div>

		<div class="campo-input">
			<label class="fw-600 fs-16 green-50">Celular ou fixo</label>
			<div class="input-container-identificacao">
				<div class="input-frame">
					<div class="dropdown">
						<button class="btn btn-light rounded-end-0 border-0 btn-lg dropdown-toggle" type="button"
							id="telefone2-ddi" data-bs-toggle="dropdown" aria-expanded="false">
							+55
						</button>

						<?php include_once 'ddi2.php'; ?>
					</div>


					<input type="text" name="telefone2" class="input-identificacao telefone2 fs-12 fw-400 green-50 bg-beige-50"
						required="required" inputmode="numeric">
				</div>
			</div>
		</div>


		<div class="campo-input">
			<label class="fw-600 fs-16 green-50">CEP</label>
			<div class="input-container-identificacao">
				<div class="input-frame">
					<input type="text" name="cep" class="input-identificacao cep fs-12 fw-400 green-50 bg-beige-50"
						required="required" inputmode="numeric">
				</div>
			</div>
		</div>

		<div class="campo-input">
			<label class="fw-600 fs-16 green-50">Cidade</label>
			<div class="input-container-identificacao">
				<div class="input-frame">
					<input type="text" name="cidade" id="inputCidade" class="input-identificacao cidade fs-12 fw-400 green-50 bg-beige-50"
						required="required" autocomplete="off" />
					<input type="hidden" name="idCidade" />
				</div>
			</div>
			<div id="autoCompleteCidade"></div>
			
		</div>

		<div class="campo-input">
			<label class="fw-600 fs-16 green-50">Senha</label>
			<div class="input-container-identificacao">
				<div class="input-frame">
					<input type="password" name="senha1" class="input-identificacao senha1 fs-12 fw-400 green-50 bg-beige-50"
						required="required" autocomplete="off">
				</div>
			</div>
		</div>

		<div class="campo-input">
			<label class="fw-600 fs-16 green-50">Repita a senha</label>
			<div class="input-container-identificacao">
				<div class="input-frame">
					<input type="password" name="senha2" class="input-identificacao senha2 fs-12 fw-400 green-50 bg-beige-50"
						required="required" autocomplete="off">
				</div>
			</div>
			<div class="form-text fs-14 fw-400 green-30"> A senha deve ter no mínimo 6 dígitos. </div>
		</div>

		<div class="campo-input mt-3">
			<div class="form-check">
				<input class="form-check-input" type="checkbox" value="" id="checkAcordo" required="required">
				<label class="form-check-label fs-14 fw-400 green-30" for="checkAcordo">
					Declaro que li e estou de acordo com os
					<a href="https://flusshaus.com.br/land/politica-de-privacidade/" target="_blank">
						Termos de Uso e Política de Privacidade.
					</a>
				</label>
			</div>

			<div class="form-check mb-3">
				<input class="form-check-input" type="checkbox" value="" id="marketing" checked="checked">
				<label class="form-check-label fs-14 fw-400 green-30" for="marketing">
					Aceito receber novidades e ofertas por e-mail.
				</label>
			</div>
		</div>
	</div>
	
	<div id="msg-erro" class="msg-erro" style="display: none; color: #8A2E36; font-size: 12px; margin: 12px 0; padding: 8px; background: rgba(138, 46, 54, 0.1); border-radius: 4px;"></div>
	
	<hr class="mb-3">
	
	<div class="d-flex flex-column align-items-end mt-12">
		<button type="button" name="enviar" class="btn-default fs-18 fw-500" onclick="processarCadastroEIniciarPagamento()">
			<span class="fs-18 fw-500 beige-20 uppercase">finalizar e seguir pagamento com</span>
			<img src="../../assets/images/pagbank_icon_white.png" alt="PagBank" class="pagbank-icon">
		</button>

		<a href="../data" class="btn-cancelar mt-12 fs-18 fw-500">
			CANCELAR
		</a>
	</div>
</form>

<?php include_once '../footer.php'; ?>