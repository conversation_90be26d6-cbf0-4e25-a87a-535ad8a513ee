<?php

    require_once '../config/ProjectStage.php';
    require_once '../config/Auth.php';
    
    $params = array(
        "cpf" => $_POST['cpfRecuperarSenha'],
    );
    
    $auth = new Auth();
    $resultAuth =  $auth->askAuth();
    $header = array('Authorization: Bearer ' . $resultAuth->token, "Content-Type: application/json");
    $curl = curl_init();
    
    curl_setopt($curl, CURLOPT_URL, ProjectStage::endpoint() . "/cliente/recuperar-senha/");
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
    curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($params));
    
    curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, true);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 2);
    
    $response_body = json_decode(curl_exec($curl));
    $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    if ($http_status != 200) {
        echo json_encode(array( "success" => false, "message" => $response_body->mensagem ));
    } else {
        echo json_encode(array( "success" => true, "message" => $response_body->mensagem ));
    }
?>