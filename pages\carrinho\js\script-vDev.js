$( window ).on("load", function() {
	
	const carrinho = JSON.parse(localStorage.getItem('RESERVA_APP_CARRINHO'));
				
	if (carrinho.data == null) {
		window.location.href = "../data";
	} else {
		document.getElementById("data").innerHTML = dataString(carrinho.data);
		// request(carrinho);
	}
});

$( window ).on("load", function() {
	PurchaseStepperManager.renderPurchaseStepper();
	
	const state = StepperManager.getState();
	
	if (state && state.data) {
		document.getElementById("data").innerHTML = formatarData(state.data);
		document.getElementById("horario").innerHTML = state.carrinho[0].horario;
	}
	
	if (state && state.carrinho && state.carrinho.length > 0) {
		if (state.carrinho[0].produtos && state.carrinho[0].produtos[0].experiencias && !state.carrinhoInicializado) {
			state.carrinho[0].produtos[0].experiencias = state.carrinho[0].produtos[0].experiencias.filter(exp => exp.quantidade > 0);
			state.carrinhoInicializado = true;
			StepperManager.saveState(state);
		}

		document.getElementById("nome-atrativo").innerHTML = state.carrinho[0].nome;
		mostrarIngressos(state.carrinho[0]);
		
		if (state.cupomAplicado) {
			document.getElementById('input-cupom').value = state.cupomAplicado.codigo;
		} else {
			document.getElementById('input-cupom').value = "";
		}
		
		calcularResumoValores(state.carrinho);
	} else {
		document.getElementById("nome-atrativo").innerHTML = "Carrinho vazio";
		document.getElementById("nome-atrativo-resumo").innerHTML = "Nenhum atrativo selecionado";
		document.getElementById("valor-atrativo-resumo").innerHTML = "R$ 0,00";
		document.getElementById("valor-total-resumo").innerHTML = "R$ 0,00";
		document.getElementById("cupom-codigo-display").innerHTML = "";
		document.getElementById("valor-desconto-display").innerHTML = "R$ 0,00";
	}
});

function formatarData(dataString) {
    const [ano, mes, dia] = dataString.split('-');
    const data = new Date(ano, mes - 1, dia);
    const diasSemana = ['Domingo', 'Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira', 'Sexta-feira', 'Sábado'];
    const diaSemana = diasSemana[data.getDay()];
    return `${diaSemana}, ${dia}/${mes}/${ano}`;
}

function mostrarIngressos(atrativo) {
    const template = document.querySelector('.ingresso-item');
    const container = template.parentElement;

    // Limpar itens existentes
    container.querySelectorAll('.ingresso-item:not(.d-none)').forEach(item => item.remove());
    container.querySelectorAll('hr.beige-50').forEach(hr => hr.remove());

    if (!atrativo.produtos || !atrativo.produtos[0].experiencias) return;

    const experiencias = atrativo.produtos[0].experiencias.filter(exp => exp.quantidade >= 0);

    experiencias.forEach((exp, index) => {
        const expIndex = atrativo.produtos[0].experiencias.findIndex(e => e.id === exp.id);

        // Criar novo item
        const novoItem = template.cloneNode(true);
        novoItem.classList.remove('d-none');
        novoItem.setAttribute('data-exp-index', expIndex);

        // Preencher dados
        novoItem.querySelector('.nome-ingresso').textContent = exp.nome;
        novoItem.querySelector('.quantidade-ingresso').textContent = exp.quantidade;
        novoItem.querySelector('.total-ingresso').textContent = (exp.quantidade * exp.preco_unitario).toFixed(2).replace('.', ',');
        novoItem.querySelector('.descricao-ingresso').textContent = `Valor unitário: R$ ${exp.preco_unitario.toFixed(2).replace('.', ',')}.`;

        // Configurar botões
        novoItem.querySelector('.btn-diminuir').onclick = () => alterarQuantidade(0, 0, expIndex, -1);
        novoItem.querySelector('.btn-aumentar').onclick = () => alterarQuantidade(0, 0, expIndex, 1);

        container.appendChild(novoItem);

        // Adicionar separador se não for o último
        if (index < experiencias.length - 1) {
            const separador = document.createElement('hr');
            separador.className = 'mt-3 mb-3 beige-50';
            container.appendChild(separador);
        }
    });
}


function request(carrinho) {
	jQuery.ajax({
		type: 'POST',
		dataType: 'json',
		url: "AjaxCarrinho.php",
		data: "carrinho=" + JSON.stringify(carrinho),
		cache: false,
		async: false,
		complete: function(response) {
			// Verificar se responseJSON existe
			if (!response.responseJSON) {
				document.getElementById("alerta").innerHTML = "Erro de comunicação com o servidor";
				document.getElementById("alerta").style.display = "block";
				return;
			}
			
			const responseData = response.responseJSON;
			
			if (responseData.status == 200) {
				const state = StepperManager.getState();
				if (responseData.carrinho) {
					state.cliente = responseData.carrinho;
				}
				StepperManager.saveState(state);
				
				PurchaseStepperManager.renderPurchaseStepper();
				
				window.location.href = "../identificacao";
				
				if (responseData.carrinho) {
					const carrinho = JSON.stringify(responseData.carrinho);
					html(carrinho);
					localStorage.setItem('RESERVA_APP_CARRINHO', carrinho);
				}
			} else {
				const errorMessage = responseData.message || "Erro desconhecido";
				document.getElementById("alerta").innerHTML = errorMessage;
				document.getElementById("alerta").style.display = "block";
				
				// Verificar se o botão existe antes de tentar desabilitá-lo
				const enviarButton = document.getElementsByName('enviar')[0];
				if (enviarButton) {
					enviarButton.disabled = true;
				}
			}
		},
		error: function() {
			//window.location.href = "../../erro";
		}
	});
}

function html(carrinho) {
    jQuery.ajax({
        type: 'POST',
        url: "AjaxCarrinhoHTML.php",
        data: "carrinho=" + carrinho,
        cache: false,
        async: false,
        success: function(html) {
            document.getElementById("carrinho").innerHTML = html;
        },
        error: function() {
            window.location.href = "../../erro";
        }
    });
}


function calcularResumoValores(carrinho) {
    let totalGeral = 0;
    const state = StepperManager.getState();
    
    carrinho.forEach(atrativo => {
        if (atrativo.produtos && atrativo.produtos.length > 0) {
            let totalAtrativo = 0;
            
            atrativo.produtos.forEach(produto => {
                if (produto.experiencias) {
                    produto.experiencias.forEach(exp => {
                        if (exp.quantidade > 0) {
                            totalAtrativo += exp.quantidade * exp.preco_unitario;
                        }
                    });
                }
            });
            
            document.getElementById('nome-atrativo-resumo').textContent = atrativo.nome;
            document.getElementById('valor-atrativo-resumo').textContent = `R$ ${totalAtrativo.toFixed(2).replace('.', ',')}`;
            
            totalGeral += totalAtrativo;
        }
    });
    
    document.getElementById('valor-total-resumo').textContent = `R$ ${totalGeral.toFixed(2).replace('.', ',')}`;
    
    if (state) {
        state.valorTotal = totalGeral;
        StepperManager.saveState(state);
    }
}

function finalizarEIdentificar() {
    const state = StepperManager.getState();
    
    if (!state || !state.carrinho || state.carrinho.length === 0) {
        alert('Carrinho vazio. Adicione itens antes de finalizar.');
        return;
    }
    
    let temItens = false;
    state.carrinho.forEach(atrativo => {
        if (atrativo.produtos && atrativo.produtos.length > 0) {
            atrativo.produtos.forEach(produto => {
                if (produto.experiencias) {
                    produto.experiencias.forEach(exp => {
                        if (exp.quantidade > 0) {
                            temItens = true;
                        }
                    });
                }
            });
        }
    });
    
    if (!temItens) {
        alert('Selecione pelo menos um ingresso com quantidade maior que 0 antes de finalizar.');
        return;
    }
    
    state.etapa = "identificacao";
    StepperManager.saveState(state);
    
    window.location.href = "../acessar";
}

function alterarQuantidade(atrativoIndex, produtoIndex, experienciaIndex, delta) {
    const state = StepperManager.getState();
    
    if (!state || !state.carrinho || !state.carrinho[atrativoIndex]) {
        return;
    }
    
    const experiencia = state.carrinho[atrativoIndex].produtos[produtoIndex].experiencias[experienciaIndex];
    const novaQuantidade = experiencia.quantidade + delta;
    
    if (novaQuantidade < 0) {
        return;
    }
    
    experiencia.quantidade = novaQuantidade;

    StepperManager.saveState(state);
    
    atualizarInterface();
}

function excluirAtrativo(atrativoIndex) {    
    const state = StepperManager.getState();

    state.carrinho[atrativoIndex].produtos.forEach(produto => {
        produto.experiencias.forEach(experiencia => {
            experiencia.quantidade = 0;
        });
    });
    
    StepperManager.saveState(state);
    
    const temItens = verificarCarrinhoTemItens(state);
    if (!temItens) {
        alert('Carrinho vazio! Redirecionando para seleção de atrativos...');
        window.location.href = '../atrativo';
        return;
    }
    
    atualizarInterface();
}

function verificarCarrinhoTemItens(state) {
    if (!state || !state.carrinho) return false;
    
    return state.carrinho.some(atrativo => 
        atrativo.produtos && atrativo.produtos.some(produto => 
            produto.experiencias && produto.experiencias.some(exp => exp.quantidade > 0)
        )
    );
}

function cancelarCarrinho() {
        const state = StepperManager.getState();
        if (state) {
            delete state.carrinho;
            delete state.cupomAplicado;
            StepperManager.saveState(state);
        }
        
        window.location.href = '../';
}

function atualizarInterface() {
    const state = StepperManager.getState();
    
    if (state && state.carrinho && state.carrinho.length > 0) {
        document.getElementById("nome-atrativo").innerHTML = state.carrinho[0].nome;
        
        mostrarIngressos(state.carrinho[0]);
        
        calcularResumoValores(state.carrinho);
    } else {
        document.getElementById("nome-atrativo").innerHTML = "Carrinho vazio";
        document.getElementById("nome-atrativo-resumo").innerHTML = "Nenhum atrativo selecionado";
        document.getElementById("valor-atrativo-resumo").innerHTML = "R$ 0,00";
        document.getElementById("valor-total-resumo").innerHTML = "R$ 0,00";
        document.getElementById("cupom-codigo-display").innerHTML = "-";
        document.getElementById("valor-desconto-display").innerHTML = "R$ 0,00";
        document.getElementById("input-cupom").value = "";
        
        const container = document.getElementById('container-ingressos');
        const existentes = container.querySelectorAll('.ingresso-item:not(.d-none)');
        existentes.forEach(item => item.remove());
        
        const hrs = container.querySelectorAll('hr.beige-50');
        hrs.forEach(hr => hr.remove());
    }
    
    PurchaseStepperManager.renderPurchaseStepper();
}

function criarCarrinho(state) {
	const carrinhoData = {
		cliente: state.cliente || "",
		data_ingresso: state.data,
		codigo_transacao: "",
		atrativos: []
	};
	
	state.carrinho.forEach(atrativo => {
		if (atrativo.produtos && atrativo.produtos[0].experiencias) {
			const produtos = [];
			
			atrativo.produtos[0].experiencias.forEach(exp => {
				if (exp.quantidade >= 0) { 
					produtos.push({
						id: exp.id,
						quantidade: exp.quantidade
					});
				}
			});
			
			carrinhoData.atrativos.push({
				id: atrativo.id_atrativo,
				horario: atrativo.horario,
				produtos: produtos
			});
		}
	});
	
	jQuery.ajax({
		type: 'POST',
		dataType: 'json',
		url: "../carrinho/AjaxCriarCarrinho.php",
		data: "carrinho=" + JSON.stringify(carrinhoData),
		cache: false,
		async: true,
		success: function(response) {
			iniciarPagamentoPagBank();
		},
		error: function() {
			console.log("Erro ao criar carrinho na API");
		}
	});
}
