<?php
    require_once '../../config/ProjectStage.php';
    require_once '../../config/Auth.php';
    
    $auth = new Auth();
    $resultAuth =  $auth->askAuth();
    $endpoint = ProjectStage::endpoint() . "/atrativo/produto/" . $_POST['atrativo'] . "/" . $_POST['data'];
    $curl = curl_init();
    
    curl_setopt($curl, CURLOPT_URL, $endpoint);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HTTPHEADER, array('Authorization: Bearer ' . $resultAuth->token));
    curl_setopt($curl, CURLOPT_HTTPGET, 1);
    
    $response_body = curl_exec($curl);
    curl_close($curl);
    
    foreach (json_decode($response_body) as $key => $value) {
?>

    <label class="fs-14 fw-600 green-50 label-nome"> <?php echo $value->nome; ?>
        <i class="bi bi-info-circle fs-14 burgundy-50 more-info-icon"></i>
    </label>

    <input type="hidden" value="<?php echo $value->id; ?>" name="idPreco">
    <input type="hidden" value="<?php echo $value->preco; ?>" name="precoUnitario">
    <input type="hidden" value="<?php echo $value->nome; ?>" name="nomePreco">

    <div class="mt-2 d-flex flex-row gap-2 align-items-center">
        <div class="input-quantidade radius-24 bg-beige-50">
            <button class="bg-beige-30 w-h-32" type="button" onclick="sub(<?php echo $key; ?>)">
                <i class="bi bi-dash green-50"></i>
            </button>

            <input type="text" name="quantidade" inputmode="numeric" required="required"
      		   class="input-quantidade-valor fs-14 fw-500 green-60 bg-beige-50" value="0">

            <button class="bg-beige-30 w-h-32" type="button" onclick="sum(<?php echo $key; ?>)">
                <i class="bi bi-plus green-50"></i>
            </button>
        </div>
        
        <hr>
        
        <div class="valor-total fs-14 fw-500 green-60 bg-orange-50 radius-24 padding-10-14">
            R$ <span class="fw-500 " id="precoFinal<?php echo $key; ?>"><?php echo number_format($value->preco, 2, ',', '.'); ?></span>
        </div>
    </div>
    
    <?php foreach ($value->experiencias as $key => $experiencia) { ?>
    	<span class="descricao-label">
    		<?php echo $experiencia->nome; ?> <?php echo number_format($experiencia->preco, 2, ',', '.'); ?>
    	
    		<?php if ($key < count($value->experiencias) - 1) { ?>
    			+
    		<?php } ?>
    	</span>
    <?php } ?>
    
	<?php if ($key < count(json_decode($response_body)) - 1) { ?>
        	<hr class="mb-3 mt-3">
	<?php } ?>
<?php } ?>