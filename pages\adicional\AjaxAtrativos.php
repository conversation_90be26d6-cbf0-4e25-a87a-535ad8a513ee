<?php

    require_once '../../config/ProjectStage.php';
    require_once '../../config/Auth.php';
    
    $carrinhoList = json_decode($_POST['carrinho']);
    $atrativoIgnorado = array();
    
    foreach ($carrinhoList as $carrinho) {
        array_push($atrativoIgnorado, $carrinho->id);
    }
    
    $params = array(
        "idAtrativoPrincipal" => $_POST['atrativo'],
        "data" => $_POST['data'],
        "atrativoIgnorado" => $atrativoIgnorado
    );
    
    $auth = new Auth();
    $resultAuth =  $auth->askAuth();
    $endpoint = ProjectStage::endpoint() . "/atrativo/adicional";
    $curl = curl_init();
    
    curl_setopt($curl, CURLOPT_URL, $endpoint);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HTTPHEADER, array('Authorization: Bearer ' . $resultAuth->token, 'Content-Type: application/json'));
    curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'GET');
    curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($params));
    
    $response_body = curl_exec($curl);
    curl_close($curl);
    
    $atrativoList = json_decode($response_body);
    
    foreach ($atrativoList as $atrativo) {
?>
    	<div class="card overflow-hidden border-0 mb-3">
        	<div class="row g-0">
            	<div class="col-auto">
              		<img src="<?php echo $atrativo->imagem; ?>" style="height: 134px;" class="img-fluid" alt="...">
            	</div>
            	
            	<div class="col">
              		<div class="card-body">
                		<p class="fw-semibold"> <?php echo $atrativo->nome; ?> </p>
                		<p class="text-tertiary text-one-line fs-6"> <?php echo $atrativo->descricao; ?> </p>
                		<hr style="margin: 0.7rem 0;"/>
                		<span class="btn btn-sm bg-tertiary rounded-2">
                		 	<?php echo str_replace("00", "", str_replace(":", "h", $atrativo->abertura)); ?> às <?php echo str_replace("00", "", str_replace(":", "h", $atrativo->fechamento)); ?>
                		</span>
                		
                		<span class="btn btn-sm bg-quaternary-border rounded-2" data-bs-toggle="modal" data-bs-target="#modalAtrativoInformacao" data-bs-informacao="<?php echo $atrativo->informacao; ?>">
                		 	+ informações e valores
                		</span>
              		</div>
            	</div>
            	
            	<div class="col-auto bg-secondary atrativo-arrow">
            		<button type="button" class="btn bi bi-chevron-right text-white fs-3" id="btn-<?php echo $atrativo->id; ?>" data-objeto='<?= json_encode($atrativo) ?>' onclick="select(<?php echo $atrativo->id; ?>)"></button>
            	</div>
          	</div>
        </div>
<?php } ?>