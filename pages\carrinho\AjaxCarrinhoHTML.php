<?php
    $carrinho = json_decode($_POST['carrinho']);
    
    $total = 0;
    
    foreach ((array) $carrinho->atrativos as $value) {
        foreach ($value->precos as $key => $preco) {
            $total += $preco->precoUnitario * $preco->quantidade;
        }
    
    ?>
    <div class="card overflow-hidden border-0 mb-3">
    	<div class="card-body">
    		<p class="fw-600"><?php echo $value->nome; ?></p>
    		
    		<hr class="my-2"/>
    		
    		<?php foreach ($value->precos as $key => $preco) { ?>
    			<label class="fs-6"> <?php echo $preco->nome; ?> </label>
        		<div class="input-group mb-2">
              		<button type="button" class="input-group-text" onclick="sub(<?php echo $key; ?>)">
            			<i class="bi bi-dash-lg"></i>
              		</button>
              		
                  	<input type="text" name="quantidade" value="<?php echo $preco->quantidade; ?>" inputmode="numeric"
                  		   required="required" class="form-control text-center">
                  	
                  	<button type="button" class="input-group-text rounded-end-2" onclick="sum(<?php echo $key; ?>)">
                  		<i class="bi bi-plus-lg"></i>
                  	</button>
                  	
                  	<span class="input-group-text bg-quaternary rounded-2 ms-3 fs-6" style="width: 130px;">
                		R$ <label id="precoFinal<?php echo $key; ?>" class="fw-500 ms-1"><?php echo number_format($preco->precoUnitario * $preco->quantidade, 2, ',', '.'); ?></label>
                	</span>
                	
                	<span class="input-group-text bg-tertiary-border rounded-2 ms-3">
                		<i class="bi bi-trash3"></i>
                	</span>
                </div>
            <?php } ?>
    	</div>
    </div>
    
    <div class="modal fade" id="modalCarrinho<?php echo $value->idAtrativo; ?>">
    	<div class="modal-dialog">
    		<div class="modal-content">
    			<div class="modal-header">
    				<h1 class="modal-title fs-5" id="exampleModalLabel">Modal title</h1>
    				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
    			</div>
    			
    			<div class="modal-body">
                    <div>
                        <label class="fs-7"> Atrativo: </label>
                        <label class="fw-600 fs-6"> <?php echo $value->nome; ?>  </label>
                    </div>
                    
                    <?php foreach ($value->precos as $preco) { ?>
                    	<div>
                            <label class="fs-7"> <?php echo $preco->nome; ?>: </label>
                            <label class="fw-600 fs-6"> <?php echo $preco->quantidade; ?>  </label>
                            <label class="fw-600 fs-6"> (R$ <?php echo number_format($preco->quantidade * $preco->precoUnitario, 2, ',', '.'); ?>) </label>
                        </div>
                    <?php } ?>
    			</div>
    			
    			<div class="modal-footer">
    				<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
    				<button type="button" class="btn btn-primary">Save changes</button>
    			</div>
    		</div>
    	</div>
    </div>
<?php } ?>

<div class="row align-items-end mb-3">
	<div class="col-sm-12 col-md-6 col-lg-6 d-none d-md-block">
		cupom
	</div>
	
	<div class="col-sm-12 col-md-6 col-lg-6 text-end fw-600">
    	<span class="fw-300">Total: </span> R$ <label id="total" class="fw-600"> <?php echo number_format($total, 2, ',', '.'); ?> </label>
	</div>
</div>