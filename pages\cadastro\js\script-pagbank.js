function iniciarPagamentoPagBank() {
  console.log('Iniciando pagamento...');
  let dados;
  try {
    const dadosBrutos = localStorage.getItem('RESERVA_APP_CARRINHO');
    console.log('Dados brutos do localStorage:', dadosBrutos);
    if (!dadosBrutos) {
      throw new Error('Dados da reserva não encontrados no localStorage.');
    }

    dados = JSON.parse(dadosBrutos);
    console.log('Dados parseados:', dados);

    // Se dados é array, pega o primeiro elemento
    if (Array.isArray(dados)) {
      dados = dados[0];
      console.log('Dados após correção de array:', dados);
    }

    if (!dados.cliente || !dados.carrinho) {
      throw new Error('Objeto da reserva está incompleto (faltando cliente ou carrinho).');
    }
    console.log('Dados da reserva validados:', dados);

  } catch (error) {
    console.error('Erro ao processar dados:', error);
    const msgErro = document.getElementById("msg-erro");
    if (msgErro) {
      msgErro.innerHTML = 'Ocorreu um erro ao ler os dados da sua reserva. Tente novamente.';
      msgErro.style.display = 'block';
    } else {
      alert('Ocorreu um erro ao ler os dados da sua reserva. Tente novamente.');
    }
    return;
  }

  jQuery.ajax({
    type: 'POST',
    url: window.BASE_URL + 'index.php?action=processar_pagamento',
    data: JSON.stringify(dados),
    contentType: 'application/json',
    dataType: 'json',
    success: function(response) {
      if (response && response.success && response.pay_url) { 
        window.location.href = response.pay_url;
      } else {
        const mensagemErro = response.error || 'Não foi possível criar o checkout de pagamento.';
        document.getElementById("msg-erro").innerHTML = mensagemErro;
        document.getElementById("msg-erro").style.display = 'block';

        if (response && response.debug) {
            console.error('Debug Info:', response.debug);
            if (response.debug.raw_response) {
                console.error('Raw PagBank Response:', JSON.parse(response.debug.raw_response));
            }
        }
      }
    },
    error: function(jqXHR, textStatus, errorThrown) {
      document.getElementById("msg-erro").innerHTML = 'Ocorreu um erro de comunicação. Por favor, verifique sua conexão e tente novamente.';
      document.getElementById("msg-erro").style.display = 'block';
    }
  });
}
