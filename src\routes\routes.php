<?php
require_once __DIR__ . '/../services/PagBankService.php';
require_once __DIR__ . '/../controllers/PagamentoController.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

function processarRota($action, $config, $precos) {
    $pagamentoController = new PagamentoController($config, $precos);

    switch ($action) {
        case 'processar_pagamento':
            header('Content-Type: application/json');
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $resultado = $pagamentoController->processarPagamento();
                echo json_encode($resultado);
                exit;
            } else {
                http_response_code(405); 
                echo json_encode(['success' => false, 'error' => 'Método não permitido.']);
                exit;
            }
            break;

        default:
            header('Location: index.php');
            exit;
    }
}


function responderJson($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
} 