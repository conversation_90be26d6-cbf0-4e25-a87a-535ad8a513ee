// chamada API para finalizar reserva
function finalizarReserva() {
	
	let dadosFinalizacao = [];
	//let dadosReserva = "idHorario=" + localStorage.getItem('RESERVA_APP_HORARIO') + "&quantidade=" + localStorage.getItem('RESERVA_APP_QUANTIDADE') + "&data=" + localStorage.getItem('RESERVA_APP_DATA');
	let atrativos = JSON.parse(localStorage.getItem('RESERVA_APP_CARRINHO'));
	
	dadosFinalizacao.push({"idHorario": localStorage.getItem('RESERVA_APP_HORARIO'), "quantidade": localStorage.getItem('RESERVA_APP_QUANTIDADE'), "data": localStorage.getItem('RESERVA_APP_DATA'), atrativos});
	
	if (localStorage.getItem('RESERVA_APP_RESERVA')) {
		dados += "&idReserva=" + localStorage.getItem('RESERVA_APP_RESERVA');
	}
	
    jQuery.ajax({
        type: 'POST',
        dataType: 'json',
        url: "AjaxFinalizar.php",
        data: {pedido: JSON.stringify(dadosFinalizacao)},
        cache: false,
        async: false,
        complete: function(response) {
			
			if (response.responseJSON.status == 200) {
				
				let dadoReserva = response.responseJSON.message;
				
				document.getElementById("codigo").innerHTML = dadoReserva.idReserva;
				document.getElementById("tipo").innerHTML = dadoReserva.tipoRefeicao;
				document.getElementById("data").innerHTML = dadoReserva.data;
				document.getElementById("quantidade").innerHTML = dadoReserva.numeroPessoas;
				document.getElementById("data").innerHTML = dataString(dadoReserva.data);				
				document.getElementById("horario").innerHTML = dadoReserva.hora.substring(0, 5);
				document.getElementById("nome").innerHTML = dadoReserva.cliente;
				document.getElementById("telefone").innerHTML = dadoReserva.telefone1;
				document.getElementById("email").innerHTML = dadoReserva.email;
				
				var img = document.querySelector("#imageQrCode");
            	img.setAttribute('src', "https://adm.flusshaus.com.br/repositorio-imagens/reserva/" + dadoReserva.qrCode);
				
				localStorage.clear();
				localStorage.setItem('RESERVA_APP_FINALIZADA', true);
				
			} else {
				//window.location.href = "../erro";
			}
        },
        error: function() {
			//window.location.href = "../erro";
		}
    });
}

// busca dados de reserva finalizada
function findDadosReserva(idReserva) {
	
    jQuery.ajax({
        type: 'POST',
        dataType: 'json',
        url: "AjaxReserva.php",
        data: "idReserva=" + idReserva,
        cache: false,
        async: false,
        complete: function(response) {
			
			if (response.responseJSON.status == 200) {
				
				let dadoReserva = response.responseJSON.result;
				
				document.getElementById("codigo").innerHTML = dadoReserva.idReserva;
				document.getElementById("tipo").innerHTML = dadoReserva.tipoRefeicao;
				document.getElementById("data").innerHTML = dadoReserva.data;
				document.getElementById("quantidade").innerHTML = dadoReserva.numeroPessoas;
				document.getElementById("data").innerHTML = dataString(dadoReserva.data);				
				document.getElementById("horario").innerHTML = dadoReserva.hora.substring(0, 5);
				document.getElementById("nome").innerHTML = dadoReserva.cliente;
				document.getElementById("telefone").innerHTML = dadoReserva.telefone1;
				document.getElementById("email").innerHTML = dadoReserva.email;
				
				var img = document.querySelector("#imageQrCode");
            	img.setAttribute('src', "https://adm.flusshaus.com.br/repositorio-imagens/reserva/" + dadoReserva.qrCode);
				
			} else {
				window.location.href = "../erro";
			}
        	
        },
        error: function() {
			window.location.href = "../erro";
		}
    });
}