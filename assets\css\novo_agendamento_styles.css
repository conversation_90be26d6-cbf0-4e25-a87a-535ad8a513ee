@import url('./variables.css');
@import url('./typography.css');
@import url('./utilities.css');

/* stepper */
.stepper {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  margin-bottom: 2rem;
}

.stepper .step-card {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stepper .step-circle {
  width: 32px;
  border-radius: 50%;
  background: #2f4634;
  color: #e4d7ae;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  border: 2px solid #2f4634;
}

.stepper .step-circle.active {
  background: #fff;
  color: #2f4634;
  border: 2px solid #2f4634;
}

.stepper .step-label-card {
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: #2f4634;
  text-align: center;
  font-weight: 500;
}

.stepper .step-line {
  width: 60px;
  height: 2px;
  background: #2f4634;
  margin-top: 15px;
}

.stepper .step-line.dashed {
  border-top: 2px dashed #2f4634;
  background: none;
}

.stepper-abs {
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: 0 auto 2rem auto;
}

.stepper-circles {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding: 0 25px;
}

.step-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.25rem;
  border: 2px solid #2f4634;
  z-index: 1;
  position: relative;
  transition: background 0.2s, color 0.2s;
}

.step-circle.completed,
.step-circle.active {
  background: #2f4634;
  color: #e4d7ae;
  border: 2px solid #2f4634;
}

.step-circle.upcoming {
  background: #e4d7ae;
  color: #2f4634;
  border: 2px solid #2f4634;
}

.stepper-line {
  flex: 1;
  height: 2px;
  background: #2f4634;
  z-index: 0;
  margin: 0 -2px;
}

.stepper-line-dashed {
  border-top: 2px dashed #2f4634;
  background: none;
  z-index: 1;
}

.stepper-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
}

.step-label {
  width: 80px;
  text-align: center;
  font-size: 0.75rem;
  color: #2f4634;
  font-weight: 600;
  word-break: break-word;
}

.step-circle.completed .step-label,
.step-circle.active .step-label {
  color: #e4d7ae;
}

/* purchase stepper */
.purchase-stepper-container {
  margin-bottom: 1.25rem;
}

/*header*/
.mb-custom-2 {
  margin-bottom: 2rem;
}

hr {
  color: #f4edd1;
  opacity: unset;
  margin: unset;
}

/* footer */
.author {
    font-size: 11px;
    color: #FFF;
    text-decoration: none;
    font-family: "Poppins-Light";
}
.author-sm-bg {
	background-color: rgb(235,222,181);
}
.author-sm-bg-text {
	font-size: 11px;
    text-decoration: none;
    color: rgba(33, 37, 41, 0.75);
    font-family: "Poppins-Light";
}

/* data */
.reserva-label-container {
  margin: 12px 0 28px 0;
}

.reserva-label-container .flatpickr-calendar {
  border-radius: 12px;
}

.green-font {
  color: #2f4634;
}

.btn-outlined-custom {
  	color: #8a2e36;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    border-radius: 24px;
    border: 1px solid #8a2e36;
    padding: 8px 12px;
    text-decoration: none;
}
.produto-card {
	width: 100%;
  	height: 100%;
  	padding: 16px;
  	background: #f4edd1;
  	border-radius: 12px;
  	margin-top: 12px;
}

.produto-info {
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
  display: inline-flex;
}

.produto-info-label {
  word-wrap: break-word;
}

#formAtrativo {
  margin-top: 28px;
}

.atrativo-card-body {
  position: relative;
  padding-right: 12px;
}

.atrativo-arrow {
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 12px;
  right: 12px;
}

.info-atrativo {
  max-width: 90%;
}

.modal-gastronomia-content {
    border: none;
    padding: 20px;
    display: block;
    overflow: hidden;
    position: relative;
    margin-bottom: 32px;
    border-radius: 32px !important;
	box-shadow: 0 8px 32px rgba(47, 70, 52, 0.18);
}

.modal-gastronomia-img {
  width: 100%;
  max-height: 240px;
  height: 240px;
  object-fit: cover;
  border-radius: 20px;
  display: block;
  margin-bottom: 32px;
}

.btn-close {
  box-sizing: border-box;
}

.btn-close i {
  font-size: 14px;
}

.modal-gastronomia-close {
  position: absolute;
  top: 32px;
  right: 32px;
  z-index: 2;
  background: #f4edd1;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: unset;
}

.btn-orange-50-hover:hover {
  opacity: 0.9;
  transition: all 0.3s ease-in-out;
  color: unset;
  background-color: #ffac33;
  border: none;
}

.bi-x-lg {
  font-size: 1.5rem;
}

.img-atrativo {
	width: 160px;
  	height: 100%;
  	object-fit: cover;
}

@media (max-width: 767px) {
	.img-atrativo {
		width: 100%;
	}
}

/* quantidade */
.input-quantidade {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 4px;
}

.input-quantidade button {
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.input-quantidade button i {
  font-size: 20px;
}

.input-quantidade button i::before {
  font-weight: 900 !important;
}

.valor-total {
  min-width: 6.875rem;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.descricao-label {
  font-size: 12px;
  font-family: "Poppins-Medium";
  color: #2f4634;
}

.more-info-icon {
  margin-left: 6px;
}

.more-info-icon::before {
  font-weight: 600 !important;
}

.msg-pets span {
  margin: 0 6px;
}

.msg-pets .more-info-icon {
  margin-left: 0;
}

.input-quantidade input[type="text"] {
  border: none;
  outline: none;
  width: 40px;
  text-align: center;
  background: transparent;
  padding: 0;
  margin: 0;
  -moz-appearance: textfield;
}

.input-quantidade input[type="text"]::-webkit-outer-spin-button,
.input-quantidade input[type="text"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* carrinho */

.carrinho-main-layout {
	display: flex;
	flex-direction: column;
	align-self: stretch;
	gap: 28px;
}

.carrinho-content {
	display: flex;
	flex-direction: column;
	align-self: stretch;
	gap: 20px;
}

.atrativo-card {
	display: flex;
	flex-direction: column;
	align-items: center;
	align-self: stretch;
	gap: 32px;
	padding: 32px 20px;
	background: var(--color-beige-40);
	border-radius: 32px;
	position: relative;
}

.btn-delete-atrativo {
	position: absolute;
	top: 12.13px;
	right: 12.13px;
	width: 33.37px;
	height: 33.37px;
	padding: 6px;
	background: rgba(138, 46, 54, 0.1);
	border-radius: 40px;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
}

.btn-delete-atrativo i {
	width: 13.09px;
	height: 14.73px;
	color: var(--color-burgundy-50);
}

.ingressos-section {
	display: flex;
	flex-direction: column;
	align-self: stretch;
	gap: 6px;
}

.ingresso-item-header {
	display: flex;
	align-items: center;
	gap: 6px;
}

.btn-quantidade {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 10px;
	padding: 1px 10px;
	width: 32px;
	height: 32px;
	background: var(--color-beige-30);
	border-radius: 16px;
	border: none;
}

.btn-quantidade i {
	font-size: 20px;
	font-weight: 600;
	color: var(--color-green-50);
}

.cupom-section {
	display: flex;
	justify-content: space-between;
	align-items: center;
	align-self: stretch;
	padding: 8px;
	background: var(--color-beige-50);
	border-radius: 64px;
}

.cupom-input-area {
	display: flex;
	align-items: center;
	gap: 10px;
	padding: 0px 8px;
	flex: 1;
}

#input-cupom {
	border: none;
	background: transparent;
	outline: none;
}

.btn-validar-cupom {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 10px;
	padding: 10px 14px;
	background: var(--color-green-50);
	border-radius: 24px;
	border: none;
	color: var(--color-beige-10);
}

.btn-default {
	display: flex;
	justify-content: center;
	align-items: center;
	align-self: stretch;
	padding: 14px;
	background: var(--color-green-50);
	border-radius: 44px;
	border: none;
	color: var(--color-beige-10);
	font-weight: normal;
}

.btn-default:hover {
  background-color: var(--color-green-50);
  color: var(--color-beige-10);
  opacity: 0.9;
  transition: all 0.3s ease-in-out;
}

.btn-cancelar {
	display: flex;
	justify-content: center;
	align-items: center;
	align-self: stretch;
	padding: 13px;
	background: transparent;
	border-radius: 44px;
	border: 1px solid var(--color-green-50);
	color: var(--color-green-50);
	text-decoration: none;
}

/*identificação*/
.identificacao-frame {
	display: flex;
	flex-direction: column;
	align-self: stretch;
	gap: 28px;
	max-width: 700px;
	margin: 0 auto;
	padding: 20px;
}

.header-section {
	display: flex;
	flex-direction: column;
	align-self: stretch;
	gap: 12px;
}

.header-left {
	display: flex;
	flex-direction: column;
	align-self: stretch;
	gap: 12px;
}

.header-right {
	align-self: stretch;
}

.total-card {
	display: flex;
	flex-direction: column;
	align-self: stretch;
	gap: 4px;
	padding: 16px;
	background: #F4EDD1;
	border-radius: 12px;
}

.total-info {
	display: flex;
	align-items: center;
	gap: 8px;
}

.total-label {
	font-weight: 400;
	font-size: 16px;
	line-height: 1.5em;
	color: #2F4634;
}

.total-value {
	font-weight: 600;
	font-size: 16px;
	line-height: 1.5em;
	color: #2F4634;
}

#etapa-cpf {
	display: flex;
	flex-direction: column;
	gap: 20px;
}

.campos-identificacao {
	display: flex;
	flex-direction: column;
	align-self: stretch;
	gap: 8px;
}

.campo-cpf,
.campo-senha,
.campo-input {
	display: flex;
	flex-direction: column;
	align-self: stretch;
	gap: 4px;
}

.input-container-identificacao {
	display: flex;
	align-items: center;
	align-self: stretch;
	padding: 12px 8px;
	background: var(--color-beige-50);
	border-radius: 64px;
}

.input-frame {
	display: flex;
	align-items: center;
	gap: 10px;
	padding: 0px 8px;
	width: 100%;
}

.input-identificacao {
	border: none;
	background: transparent;
	outline: none;
	width: 100%;
}

.input-identificacao::placeholder {
	color: rgba(47, 70, 52, 0.5);
}

.msg-erro {
	color: #8A2E36;
	font-size: 12px;
	margin-top: 8px;
	display: none;
}

.msg-sucesso {
	color: #2F4634;
	font-size: 12px;
	margin-top: 8px;
	display: none;
}

.links-identificacao {
	display: flex;
	flex-direction: column;
	justify-content: center;
	gap: 8px;
	margin: 28px 0 24px 0;
}

.acoes-section {
	margin-top: 12px;
	display: flex;
	flex-direction: column;
	align-self: stretch;
	gap: 12px;
}

.btn-text {
	font-family: 'Poppins-Medium';
	font-weight: 500;
	font-size: 18px;
	line-height: 1.5em;
	color: #FFFEF0;
}

.pagbank-icon {
	width: 97.82px;
	height: 27.64px;
	object-fit: contain;
	margin-left: 5px;
}

.btn-cancelar-final {
	display: flex;
	justify-content: center;
	align-items: center;
	align-self: stretch;
	gap: 10px;
	padding: 14px;
	background: transparent;
	border: 1px solid #2F4634;
	border-radius: 74px;
	text-decoration: none;
}

.modal-gastronomia-content .modal-header {
	border-bottom: 1px solid #2f463421;
}

.divider-green-50 {
	color: #2f463421;
}

.input-frame .dropdown {
	height: auto !important;
	min-height: 0 !important;
	display: inline-block !important;
	line-height: 1.2em !important;
}

.input-frame .dropdown .btn {
	border: none !important;
	background: transparent !important;
	padding: 0 !important;
	font-family: 'Poppins-Regular' !important;
	font-weight: 400 !important;
	font-size: 12px !important;
	line-height: 1.2em !important;
	color: #2F4634 !important;
	border-radius: 0 !important;
	box-shadow: none !important;
	min-height: 0 !important;
	height: auto !important;
	vertical-align: baseline !important;
}

.input-frame .dropdown .btn:hover,
.input-frame .dropdown .btn:focus,
.input-frame .dropdown .btn:active,
.input-frame .dropdown .btn.show {
	background: transparent !important;
	border: none !important;
	box-shadow: none !important;
	color: #2F4634 !important;
}

.input-frame .dropdown .btn::after {
	margin-left: 4px;
	border-top-color: #2F4634;
}

.input-frame .dropdown .dropdown-menu {
	border-radius: 8px;
	border: 1px solid #F4EDD1;
	box-shadow: 0 4px 12px rgba(47, 70, 52, 0.1);
	padding: 8px 0;
	font-family: 'Poppins-Regular';
	font-size: 12px;
}

.input-frame .dropdown .dropdown-item {
	padding: 8px 16px;
	color: #2F4634;
	font-size: 12px;
	line-height: 1.5em;
}

.input-frame .dropdown .dropdown-item:hover {
	background-color: #FDFDEE;
	color: #2F4634;
}

.input-identificacao{
	appearance: base-select;
	border: none;
	background: transparent;
	outline: none;
	width: 100%;;
	font-weight: 400;
	font-size: 12px;
	color: #2F4634;
	padding: 0;
	border-radius: 0;
	box-shadow: none;
	cursor: pointer;
	transition: all 0.2s ease;
	display: flex;
	justify-content: space-between;

}

.input-identificacao::picker(select) {
	appearance: base-select;
	background: #F4EDD1;
	border: 1px solid #2F4634;
	border-radius: 12px;
	padding: 0.5rem 0;
	margin-top: 0.25rem;
	box-shadow: 0 4px 12px rgba(47, 70, 52, 0.15);
	cursor: default;
	transition: opacity 225ms ease-in-out, transform 225ms ease-in-out;
	transform-origin: top;
	transform: translateY(0) scale(1);
	opacity: 1;
	outline: none;
	overflow: hidden;
}

@starting-style {
	.input-identificacao::picker(select):popover-open {
		transform: translateY(-0.5rem) scale(0.95);
		opacity: 0;
	}
}

.input-identificacao::picker-icon {
	content: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='8' viewBox='0 0 12 8' fill='none'%3E%3Cpath d='M1.41 0L6 4.59 10.59 0 12 1.41l-6 6-6-6z' fill='%232F4634'/%3E%3C/svg%3E");

	margin-left: 8px;
	transform-origin: center;
	transition: transform 0.2s ease;
	display: inline-block;
}

.input-identificacao:open::picker-icon {
	transform: rotate(180deg);
	transform-origin: center center;
}

.input-identificacao:focus {
	outline: none;
	background: rgba(244, 237, 209, 0.3);
}

.input-container-identificacao:has(:focus) {
	background: rgba(244, 237, 209, 0.8);
	transform: scale(1.01);
	box-shadow: 0 2px 8px rgba(47, 70, 52, 0.1);
	transition: all 0.2s ease;
}

.input-identificacao option {
	background: transparent;
	color: #2F4634;
	font-family: 'Poppins-Regular';
	font-size: 12px;
	padding: 0.5rem 1rem;
	border: none;
	outline: none;
	cursor: pointer;
	transition: background-color 0.15s ease;
	box-shadow: none;
}

.input-identificacao option:hover {
	background: #E8DBC4;
	color: #2F4634;
	outline: none;
	border: none;
}

.input-identificacao option:checked {
	background: #2F4634;
	color: #F4EDD1;
	font-weight: 500;
	outline: none;
	border: none;
}

.input-identificacao option:focus {
	background: #E8DBC4;
	color: #2F4634;
	outline: none;
	border: none;
	box-shadow: none;
}

@supports not (appearance: base-select) {
	.input-identificacao {
		appearance: none;
		-webkit-appearance: none;
		-moz-appearance: none;
		background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='8' viewBox='0 0 12 8'%3E%3Cpath fill='%232F4634' d='M1.41 0L6 4.59 10.59 0 12 1.41l-6 6-6-6z'/%3E%3C/svg%3E");
		background-repeat: no-repeat;
		background-position: right 12px center;
		background-size: 12px 8px;
		padding-right: 32px;
	}
	
	.input-identificacao:hover {
		background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='8' viewBox='0 0 12 8'%3E%3Cpath fill='%23406442' d='M1.41 0L6 4.59 10.59 0 12 1.41l-6 6-6-6z'/%3E%3C/svg%3E");
	}
	
	.input-identificacao:focus {
		box-shadow: 0 0 0 2px rgba(47, 70, 52, 0.1);
		background-color: rgba(244, 237, 209, 0.3);
	}
	
	.input-identificacao option {
		background: #F4EDD1;
		color: #2F4634;
		padding: 8px 12px;
		border: none;
		outline: none;
		box-shadow: none;
	}
	
	.input-identificacao option:checked {
		background: #2F4634;
		color: #F4EDD1;
		border: none;
		outline: none;
	}
	
	.input-identificacao option:focus,
	.input-identificacao option:hover {
		background: #E8DBC4;
		color: #2F4634;
		border: none;
		outline: none;
		box-shadow: none;
	}
}

/* Autocomplete de cidade */
#autoCompleteCidade {
	position: relative;
	z-index: 1000;
}

#autoCompleteCidade .autocomplete {
	background: #F4EDD1;
	border: 1px solid #2F4634;
	border-radius: 8px;
	margin-top: 4px;
	box-shadow: 0 4px 8px rgba(47, 70, 52, 0.1);
	max-height: 200px;
	overflow-y: auto;
}

#autoCompleteCidade .autocomplete ul {
	list-style: none;
	margin: 0;
	padding: 0;
}

#autoCompleteCidade .autocomplete li {
	padding: 8px 12px;
	cursor: pointer;
	font-family: 'Poppins-Regular';
	font-size: 12px;
	color: #2F4634;
	border-bottom: 1px solid rgba(47, 70, 52, 0.1);
	transition: background-color 0.15s ease;
}

#autoCompleteCidade .autocomplete li:last-child {
	border-bottom: none;
}

#autoCompleteCidade .autocomplete li:hover {
	background: #E8DBC4;
	color: #2F4634;
}

#autoCompleteCidade .autocomplete li span {
	font-weight: 500;
}