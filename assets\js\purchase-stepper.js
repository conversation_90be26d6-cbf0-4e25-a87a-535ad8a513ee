const purchaseSteps = [
	{ id: 1, label: "carrinho", route: "carrinho" },
	{ id: 2, label: "identificação", route: "identificacao" },
	{ id: 3, label: "pagamento", route: "pagamento" },
	{ id: 4, label: "confirmação", route: "confirmacao" }
];

const PurchaseStepperManager = {
	renderPurchaseStepper() {
		const stepper = document.getElementById('purchase-stepper');
		if (!stepper) return;

		const currentPurchaseStep = this.getCurrentPurchaseStep();

		stepper.className = 'purchase-stepper d-flex align-items-center justify-content-between';
		stepper.innerHTML = '';

		purchaseSteps.forEach((step, idx) => {
			const stepText = document.createElement('span');
			stepText.textContent = step.label;
			stepText.className = 'fw-700 fs-16 text-uppercase';
			
			if (step.id <= currentPurchaseStep) {
				stepText.classList.add('burgundy-50');
			} else {
				stepText.classList.add('green-50');
			}

			stepper.appendChild(stepText);

			if (idx < purchaseSteps.length - 1) {
				const arrow = document.createElement('i');
				arrow.className = 'bi bi-arrow-right-circle-fill';
				arrow.style.fontSize = '20px';
				
				if (step.id <= currentPurchaseStep) {
					arrow.classList.add('burgundy-50');
				} else {
					arrow.classList.add('green-50');
				}
				
				stepper.appendChild(arrow);
			}
		});
	},

	getCurrentPurchaseStep() {
		const currentPage = window.location.pathname;
		const state = this.getState();
		
		if (currentPage.includes('carrinho')) return 1;
		if (currentPage.includes('acessar') || currentPage.includes('identificacao') || currentPage.includes('cadastro')) {
			return 2;
		}
		if (currentPage.includes('pagamento')) return 3;
		if (currentPage.includes('confirmacao')) return 4;
		return 1; 
	},

	getCompletedPurchaseSteps() {
		const state = this.getState();
		const completed = [];
		
		if (state.carrinho && state.carrinho.length > 0) completed.push(1);
		if (state.cliente) completed.push(2);
		
		return completed;
	},

	getState() {
		const STORAGE_KEY = 'RESERVA_APP_CARRINHO';
		return JSON.parse(localStorage.getItem(STORAGE_KEY) || '{"data": null, "cliente": null, "carrinho": [], "horario": null}');
	}
};

document.addEventListener('DOMContentLoaded', () => {
	if (document.getElementById('purchase-stepper')) {
		PurchaseStepperManager.renderPurchaseStepper();
	}
}); 