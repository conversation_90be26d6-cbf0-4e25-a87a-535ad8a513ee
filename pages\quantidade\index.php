<?php require_once '../header.php';  ?>

<script src="../../assets/js/stepper.js"></script>

<script src="js/script-vDev.js"></script>

<div class="reserva-label-container">
	<div class="d-flex mb-2">
    	<a href="../data" class="btn-outlined-custom fw-500">
    		<i class="bi bi-chevron-left me-1"></i> Escolher outro produto
    	</a>
	</div>

	<div class="produto-card">
		<div class="produto-info d-flex flex-column align-items-start gap-1">
			<div class="d-md-flex align-items-md-center gap-2">
				<div class="fs-16 fw-400 green-50">Produto:</div>
				<div id="atrativo" class="fs-16 fw-600 green-50"></div>
			</div>
			
			<div class="d-flex align-items-center gap-2">
				<div class="fs-16 fw-400 green-50">Data:</div>
				<div id="data" class="fs-16 fw-600 green-50"></div>
			</div>
		</div>
	</div>
</div>

<form id="formQuantidade">
	<div class="fs-18 fw-700 green-60 uppercase mb-20">
		Informe a quantidade de pessoas:
	</div>
	
	<div class="mb-4" id="produtoList"></div>
	
	<div class="fs-14 fw-400 green-50 msg-pets">
		<img src="../../assets/images/pets.svg" alt="Pet Friendly">
		<span>Entre em <a href="https://wa.me/5511999999999" target="_blank" class="fw-600 green-50">contato</a> para verificar disponibilidade</span>
		<i class="bi bi-info-circle fs-14 burgundy-50 more-info-icon"></i>
	</div>

	<hr class="mb-4 mt-4">
	
	<div class="padding-6-20 bg-beige-40 radius-30 d-flex justify-content-between align-items-center">
		<div class="fs-18 fw-500 green-60 uppercase">total:</div>
		<div class="fs-24 fw-700 green-60 uppercase">R$ <span id="total" class="fw-700">0,00</span></div>
	</div>

	<div id="msg-erro" class="text-danger"></div>

	<div class="d-grid gap-2 mt-custom-12">
		<input type="submit" name="enviar" value="PROSSEGUIR AGENDAMENTO" class="btn-default fs-18 fw-500" />
	</div>
</form>

<?php require_once '../footer.php'; ?>