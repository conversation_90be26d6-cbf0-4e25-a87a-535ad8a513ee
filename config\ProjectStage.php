<?php

class ProjectStage
{

    private static $development = "DEVELOPMENT";
    private static $production = "PRODUCTION";

    public static function currentStage()
    {
        return self::$development;
    }

    public function currentVersion()
    {
        return "3.6";
    }

    public static function endpoint()
    {
        if (self::currentStage() == "PRODUCTION") {
			return "http://127.0.0.1:8080/entrada-app-api";
        }
        return "https://sandbox.hsys.dev.br/entrada-app-api";
    }
}
?>