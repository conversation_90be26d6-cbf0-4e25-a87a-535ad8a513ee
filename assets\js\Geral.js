// controle de icone de carregar pagina
document.onreadystatechange = function () {
  	var state = document.readyState;
  	
  	if (state == "complete") {
        document.getElementById('loading').style.visibility = "hidden";
    } else {
    	document.getElementById('loading').style.visibility = "visible";
    }
    
}

// valida se a reserva acabou de ser finalizada
/*jQuery( window ).on("load", function() {
	if (localStorage.getItem('RESERVA_APP_FINALIZADA') && !window.location.pathname.includes("/data") && !window.location.pathname.includes("/finalizar")) {
		window.location.href = "../../";
	}
})*/

// converte a data para padrão brasiliero e define o dia da semana
function dataString(value) {
	let dataString = value.split('-').reverse().join('/');
	let partes = dataString.split('/').map(Number);
	let data = new Date('20' + partes[2], partes[1] - 1, partes[0]);
	let dias = ["<PERSON>", "Segunda-feira", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sábado"];
	let dia<PERSON>emana = dias[data.getDay() % 7];
	return diaSemana + ", " + dataString;
}

// imprimir localStorage
jQuery( window ).on("load", function() {
	for (i = 0; i < localStorage.length; i++)   {
	    console.log(localStorage.key(i) + "=[" + localStorage.getItem(localStorage.key(i)) + "]");
	}
});