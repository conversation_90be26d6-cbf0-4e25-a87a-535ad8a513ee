<?php require_once '../header.php'; ?>

<script src="js/script-vDev.js"></script>

<div class="row mb-4">
	<div class="col">
		<div class="text-end text-quaternary">
        	4 <i class="bi bi-arrow-right"></i> 7
        </div>
	</div>
</div>

<form id="formAtrativo">
	<div class="mb-2">
		Deseja adicionar outros atrativos no dia da sua visita?
	</div>
	
	<div id="atrativos"></div>
</form>

<div class="d-grid gap-2 mt-2">
    <a href="../acessar" class="btn btn-outline-dark btn-lg">
    	PULAR ETAPA
    </a>
</div>

<div class="modal fade" id="modalAtrativoInformacao">
	<div class="modal-dialog">
    	<div class="modal-content">
      		<div class="modal-body fs-6" id="atrativoInformacao">
      		</div>
      		
      		<div class="modal-footer">
        		<button type="button" class="btn btn-success" data-bs-dismiss="modal">Entendi</button>
      		</div>
    	</div>
  	</div>
</div>

<script>
const exampleModal = document.getElementById('modalAtrativoInformacao')
if (exampleModal) {
  exampleModal.addEventListener('show.bs.modal', event => {
    // Button that triggered the modal
    const button = event.relatedTarget
    // Extract info from data-bs-* attributes
    const informacao = button.getAttribute('data-bs-informacao')
    // If necessary, you could initiate an Ajax request here
    // and then do the updating in a callback.

    // Update the modal's content.
    document.getElementById('atrativoInformacao').innerHTML = informacao;
  })
}
</script>
			
<?php require_once '../footer.php'; ?>