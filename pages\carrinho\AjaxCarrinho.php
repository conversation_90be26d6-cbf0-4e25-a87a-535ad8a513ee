<?php
    require_once '../../config/ProjectStage.php';
    require_once '../../config/Auth.php';

    try {
        if (!isset($_POST['carrinho'])) {
            echo json_encode(array("status" => 400, "message" => "Dados do carrinho não fornecidos"));
            exit;
        }

        $carrinho = json_decode($_POST['carrinho']);
        
        if (!$carrinho) {
            echo json_encode(array("status" => 400, "message" => "Dados do carrinho inválidos"));
            exit;
        }
        
        $params = array(
            "status" => "CARRINHO",
            "data" => $carrinho->data,
        );
        
        if (isset($carrinho->cliente) && $carrinho->cliente !== null) {
            $params["cliente"] = $carrinho->cliente;
        }
        
        $atrativosArray = array();
        
        foreach ($carrinho->carrinho as $atrativo) {
            $atrativoArray = array(
                "id" => isset($atrativo->id_atrativo) ? $atrativo->id_atrativo : $atrativo->id,
                "idReserva" => isset($atrativo->idReserva) ? $atrativo->idReserva : null,
                "horario" => isset($atrativo->horario) ? $atrativo->horario : null,
                "nome" => isset($atrativo->nome) ? $atrativo->nome : '',
            );
            
            $precosArray = array();
            
            if (isset($atrativo->produtos) && is_array($atrativo->produtos)) {
                foreach ($atrativo->produtos as $produto) {
                    if (isset($produto->experiencias) && is_array($produto->experiencias)) {
                        foreach ($produto->experiencias as $experiencia) {
                            $precoArray = array(
                                "idPreco" => isset($experiencia->id) ? $experiencia->id : 0,
                                "nome" => isset($experiencia->nome) ? $experiencia->nome : '',
                                "quantidade" => isset($experiencia->quantidade) ? $experiencia->quantidade : 0,
                                "precoUnitario" => isset($experiencia->preco_unitario) ? $experiencia->preco_unitario : 0,
                            );
                            
                            array_push($precosArray, $precoArray);
                        }
                    }
                }
            }
            
            $atrativoArray["precos"] = $precosArray;
            array_push($atrativosArray, $atrativoArray);
        }
        
        $params["atrativos"] = $atrativosArray;
        
        $auth = new Auth();
        $resultAuth =  $auth->askAuth();
        $header = array('Authorization: Bearer ' . $resultAuth->token, "Content-Type: application/json");
        $curl = curl_init();
        
        curl_setopt($curl, CURLOPT_URL, ProjectStage::endpoint() . "/reserva");
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($params));
        
        curl_setopt( $curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt( $curl, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt( $curl, CURLOPT_SSL_VERIFYHOST, 2);
        
        $curl_response = curl_exec($curl);
        $response_body = json_decode($curl_response);
        $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        
        curl_close($curl);
        
        if ($http_status == 200) {
            echo json_encode(array( "status" => $http_status, "carrinho" => $response_body ));
        } else {
            $errorMessage = "Erro ao processar carrinho (Status: $http_status)";
            if ($response_body && isset($response_body->mensagem)) {
                $errorMessage = $response_body->mensagem;
            } elseif ($response_body && isset($response_body->message)) {
                $errorMessage = $response_body->message;
            } elseif ($curl_response) {
                $errorMessage = "Erro da API: " . $curl_response;
            }
            
            echo json_encode(array( 
                "status" => $http_status, 
                "message" => $errorMessage
            ));
        }
        
    } catch (Exception $e) {
        echo json_encode(array("status" => 500, "message" => "Erro interno: " . $e->getMessage()));
    }
?>