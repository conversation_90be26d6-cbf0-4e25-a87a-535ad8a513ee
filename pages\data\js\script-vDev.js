$( window ).on("load", function() {
	// variaveis
	let dataFechadoList = [];
	let dataEspecialList = [];
	
	// requisicoes em api's
	let dataEspecialApi = requestDataEspecial();
	let diaSemanaFechadoApi = requestDiaSemanaFechado();
	
	// percorre datas especial e classifica
	dataEspecialApi.forEach((item) => {
		
		const dataSplit = item.data.split('-');
		
		if (item.fechado) {
			dataFechadoList.push(Date.parse(new Date(dataSplit[0], (dataSplit[1] - 1), dataSplit[2])));
    	} else {
	    	dataEspecialList.push(Date.parse(new Date(dataSplit[0], (dataSplit[1] - 1), dataSplit[2])));
		}
	});
	
	// desabilita as datas como fechadas
	function disableSpecificDays(date) {
        return dataFechadoList.includes(+date);
    }
    
	// desabilitas dias da semana e define a data na lista de datas fechadas
    function disableMyDays(date) {
		let result = false;
		diaSemanaFechadoApi.forEach((item) => {
			if (item.diaSemana == date.getDay() && dataEspecialList.indexOf(Date.parse(date)) < 0) {
				dataFechadoList.push(Date.parse(date));
				result = true;
	    	}
		});
		return result;
    }
	
	$("#date1").flatpickr({
		inline: true,
		locale: "pt",
		enableTime: false,
		minDate: "today",
		dateFormat: "Y-m-d",
		disable: [disableMyDays, disableSpecificDays],
		
		onDayCreate: function(dObj, dStr, fp, dayElem) {
			
			// define a cor para datas especiais
	        if (dataEspecialList.includes(+dayElem.dateObj)) {
	        	dayElem.className += " dataFeriado";
	        }
			
			// define a cor cinza para as datas fechadas
	        if (dataFechadoList.includes(+dayElem.dateObj)) {
	        	dayElem.className += " dataFechado";
	        }
	    },

		onChange: function(selectedDates) {
			selectedDates.forEach(function(date) {
				
				let mes = (date.getMonth() + 1) + "";
				mes = mes.length == 1 ? "0" + mes : mes;
				
				let dia = date.getDate() + "";
				dia = dia.length == 1 ? "0" + dia : dia;
				
				const dataFormatada = date.getFullYear() + "-" + mes + "-" + dia;
				
				// Salvar data usando o StepperManager
				StepperManager.saveData(dataFormatada);
				
				window.location.href = "../atrativo";
			});
		}
	});
});

function requestDataEspecial() {
	
	let result = null;
	
    jQuery.ajax({
        type: 'POST',
        dataType: 'json',
        url: "AjaxDataEspecial.php",
        data: "",
        cache: false,
        async: false,
        complete: function(response) {
			if (response.responseJSON.status == 200) {
				result = response.responseJSON.result;
			} else {
				window.location.href = "../../erro";
			}
        },
        error: function() {
			window.location.href = "../../erro";
		}
    });
    return result;
}

function requestDiaSemanaFechado() {

	let result = null;

	jQuery.ajax({
		type: 'POST',
		dataType: 'json',
		url: "AjaxFechado.php",
		data: "",
		cache: false,
		async: false,
		complete: function(response) {
			if (response.responseJSON.status == 200) {
				result = response.responseJSON.result;
			} else {
				window.location.href = "../../erro";
			}
		},
		error: function() {
			window.location.href = "../../erro";
		}
	});
	return result;
}