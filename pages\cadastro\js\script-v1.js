jQuery(document).ready(function() {	
	const cpfPreenchimento = localStorage.getItem('RESERVA_APP_DOCUMENTO_PREENCHIMENTO');
	if (cpfPreenchimento) {
		const campoCpf = document.getElementsByName('cpf')[0];
		if (campoCpf) {
			campoCpf.value = cpfPreenchimento;
			if (typeof addMaskCpf === 'function') {
				addMaskCpf();
			}
			localStorage.removeItem('RESERVA_APP_DOCUMENTO_PREENCHIMENTO');
		}
	}
	
	let ddi1InputElement = document.querySelector("#search-ddi1");
	let ddi1ListElement = document.querySelector("#ul-ddi1");
	let ddi1ItemElement = ddi1ListElement.querySelectorAll(".li-ddi1")
	
	ddi1InputElement.addEventListener("input", (e) => {
		let inputed = e.target.value.toLowerCase()
	  	ddi1ItemElement.forEach((li) => {
	    	let text = li.textContent.toLowerCase()
	    	
	    	if (text.includes(inputed)) {
	      		li.style.display = "block"
    		} else {
	      		li.style.display = "none"
	    	}
	  	});
	});
	
	let ddi2InputElement = document.querySelector("#search-ddi2");
	
	if (ddi2InputElement) {
	
		let ddi2ListElement = document.querySelector("#ul-ddi2");
		let ddi2ItemElement = ddi2ListElement.querySelectorAll(".li-ddi2")
		
		ddi2InputElement.addEventListener("input", (e) => {
			let inputed = e.target.value.toLowerCase()
		  	ddi2ItemElement.forEach((li) => {
		    	let text = li.textContent.toLowerCase()
		    	
		    	if (text.includes(inputed)) {
		      		li.style.display = "block"
	    		} else {
		      		li.style.display = "none"
		    	}
		  	});
		});
	}
});

// senha 1
jQuery(document).ready(function() {
	jQuery("#show_hide_password_1 i").on('click', function(event) {
		event.preventDefault();
		
		if ($('#show_hide_password input').attr("type") == "text") {
			$('#show_hide_password_1 input').attr('type', 'password');
			$('#show_hide_password_1 i').addClass("bi-eye-slash-fill");
			$('#show_hide_password_1 i').removeClass("bi-eye-fill");
		} else if ($('#show_hide_password_1 input').attr("type") == "password") {
			$('#show_hide_password_1 input').attr('type', 'text');
			$('#show_hide_password_1 i').removeClass("bi-eye-slash-fill");
			$('#show_hide_password_1 i').addClass("bi-eye-fill");
		}
	});
});

// senha 2
jQuery(document).ready(function() {
	jQuery("#show_hide_password_2 i").on('click', function(event) {
		event.preventDefault();
		
		if ($('#show_hide_password input').attr("type") == "text") {
			$('#show_hide_password_2 input').attr('type', 'password');
			$('#show_hide_password_2 i').addClass("bi-eye-slash-fill");
			$('#show_hide_password_2 i').removeClass("bi-eye-fill");
		} else if ($('#show_hide_password_2 input').attr("type") == "password") {
			$('#show_hide_password_2 input').attr('type', 'text');
			$('#show_hide_password_2 i').removeClass("bi-eye-slash-fill");
			$('#show_hide_password_2 i').addClass("bi-eye-fill");
		}
	});
});

function processarCadastroEIniciarPagamento() {
	console.log('FLUXO INICIADO: processarCadastroEIniciarPagamento()');

	const btnEnviar = document.getElementsByName('enviar')[0];
	btnEnviar.disabled = true;
	const originalBtnText = btnEnviar.querySelector('.fs-18').textContent;
	btnEnviar.querySelector('.fs-18').textContent = "PROCESSANDO...";

	let dadosParaAjax;
	try {
		const form = document.getElementById('cadastrarCliente');
		const formData = new FormData(form);
		const ddi1 = document.getElementById("telefone1-ddi").innerHTML;
		const ddi2 = document.getElementById("telefone2-ddi").innerHTML;
		
		let checkbox = document.getElementsByName('dadosAcordo')[0];
		let marketing = checkbox ? checkbox.checked : false;

		const dadosCliente = {
			nome: formData.get('nome'),
			sobrenome: formData.get('sobrenome'),
			estrangeiro: formData.get('estrangeiro'),
			documento: formData.get('cpf'),
			email: formData.get('email'),
			telefone1: formData.get('telefone1'),
			telefone2: formData.get('telefone2'),
			cep: formData.get('cep'),
			cidade: formData.get('cidade'),
			idCidade: formData.get('idCidade'),
			ddi1: ddi1.trim(),
			ddi2: ddi2.trim(),
			senha: formData.get('senha1'),
			marketing: marketing
		};

		const STORAGE_KEY = 'RESERVA_APP_CARRINHO';
		let estadoAtual = JSON.parse(localStorage.getItem(STORAGE_KEY) || '{}');
		if (Array.isArray(estadoAtual)) {
			estadoAtual = estadoAtual[0] || {};
		}
		estadoAtual.cliente = dadosCliente;
		localStorage.setItem(STORAGE_KEY, JSON.stringify(estadoAtual));
		console.log('Dados do cliente salvos no localStorage:', estadoAtual);
		
		dadosParaAjax = jQuery(form).serialize() + "&ddi1=" + dadosCliente.ddi1 + "&ddi2=" + dadosCliente.ddi2;
	} catch (error) {
		console.error('ERRO na Etapa 1 (LocalStorage):', error);
		document.getElementById("msg-erro").innerHTML = 'Ocorreu um erro ao salvar seus dados. Tente novamente.';
		document.getElementById("msg-erro").style.display = 'block';
		btnEnviar.disabled = false;
		btnEnviar.querySelector('.fs-18').textContent = originalBtnText;
		return;
	}

	jQuery.ajax({
		type: 'POST',
		dataType: 'json',
		url: "AjaxCadastro.php",
		data: dadosParaAjax,
		cache: false,
		success: function(response) {
			if (response.success) {
				let estadoAtual = JSON.parse(localStorage.getItem('RESERVA_APP_CARRINHO') || '{}');
				estadoAtual.cliente = response.message; // ID do cliente (ex: "146222")
				localStorage.setItem('RESERVA_APP_CARRINHO', JSON.stringify(estadoAtual));

				criarCarrinho(estadoAtual);

				iniciarPagamentoPagBank();
			} else {
				document.getElementById("msg-erro").innerHTML = response.message || "Não foi possível realizar o cadastro.";
				document.getElementById("msg-erro").style.display = 'block';
				btnEnviar.disabled = false;
				btnEnviar.querySelector('.fs-18').textContent = originalBtnText;
			}
		},
		error: function(jqXHR, textStatus, errorThrown) {
			document.getElementById("msg-erro").innerHTML = 'Não foi possível comunicar com o servidor. Tente novamente.';
			document.getElementById("msg-erro").style.display = 'block';
			btnEnviar.disabled = false;
			btnEnviar.querySelector('.fs-18').textContent = originalBtnText;
		}
	});
}

// autocomplete
jQuery(document).ready(function() {
	jQuery("#inputCidade").keyup(function() {
		
		if ($(this).val().length > 2) {
		
		    jQuery.ajax({
		        type: 'POST',
		        url: "autocompleteCidade.php",
		        data: "query=" + document.getElementsByName('cidade')[0].value,
		        cache: false,
		        beforeSend: function() {
					document.getElementById('loading').style.visibility = "visible";
					document.getElementById('autoCompleteCidade').innerHTML = null;
		     	},
		        success: function(html) {
					document.getElementById('autoCompleteCidade').innerHTML = html;
					document.getElementById('loading').style.visibility = "hidden";
		        },
		    });
		    return false;
	    } else {
			document.getElementsByName('idCidade')[0].value = null;
			document.getElementById('loading').style.visibility = "hidden";
		}
    });
});

function selecionarCidade(id, nome) {
	document.getElementsByName('cidade')[0].value = nome;
	document.getElementsByName('idCidade')[0].value = id;
	document.getElementById('autoCompleteCidade').innerHTML = null;
}

// ddi telefone
function ddi1(number) {
	document.getElementById("telefone1-ddi").innerHTML = number;
	document.getElementById("telefone1-ddi").value = number;
}

function ddi2(number) {
	document.getElementById("telefone2-ddi").innerHTML = number;
	document.getElementById("telefone2-ddi").value = number;
}

// estrangeiro
$( window ).on("load", function() {
	
	changeEstrangeiro();
	
	var select = document.getElementById('estrangeiro');
	
	if (select) {
		select.addEventListener('change', changeEstrangeiro);
	}
	
	function changeEstrangeiro() {
	    
	    var x = document.getElementById("estrangeiro");
	        
	    if (x && x.value == ("1")) {
			
	        const labelCpf = document.querySelector('.campo-input:has([name="cpf"]) label');
	        if (labelCpf) labelCpf.innerHTML = 'Documento';

	        const camposCep = document.querySelector('.campo-input:has([name="cep"])');
	        const camposTelefone2 = document.querySelector('.campo-input:has([name="telefone2"])');
	        const camposCidade = document.querySelector('.campo-input:has([name="cidade"])');
	        
	        if (camposCep) camposCep.style.display = 'none';
	        if (camposTelefone2) camposTelefone2.style.display = 'none';
	        if (camposCidade) camposCidade.style.display = 'none';
	        
			document.getElementsByName("cep")[0].required = false;
			document.getElementsByName("telefone2")[0].required = false;
			document.getElementsByName("cidade")[0].required = false;
			document.getElementsByName("idCidade")[0].required = false;
	        
			if (typeof removeMaskTelefone === 'function') removeMaskTelefone();
			if (typeof removeMaskCpf === 'function') removeMaskCpf();
			ddi1('1');
			ddi2('');
			
	    } else {
			
			const labelCpf = document.querySelector('.campo-input:has([name="cpf"]) label');
			if (labelCpf) labelCpf.innerHTML = 'CPF';

	        const camposCep = document.querySelector('.campo-input:has([name="cep"])');
	        const camposTelefone2 = document.querySelector('.campo-input:has([name="telefone2"])');
	        const camposCidade = document.querySelector('.campo-input:has([name="cidade"])');
	        
	        if (camposCep) camposCep.style.display = 'flex';
	        if (camposTelefone2) camposTelefone2.style.display = 'flex';
	        if (camposCidade) camposCidade.style.display = 'flex';
			
			document.getElementsByName("cep")[0].required = true;
			document.getElementsByName("telefone2")[0].required = true;
			document.getElementsByName("cidade")[0].required = true;
			document.getElementsByName("idCidade")[0].required = true;
			
			if (typeof addMaskTelefone === 'function') addMaskTelefone();
			if (typeof addMaskCpf === 'function') addMaskCpf();
			ddi1('55');
			ddi2('55');
		}
	}
});