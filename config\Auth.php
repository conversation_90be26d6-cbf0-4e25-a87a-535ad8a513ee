<?php

class Auth
{

    public function askAuth()
    {
        try {

            $params = json_encode(array(
                "nome" => "flusshaus-agendamento",
                "senha" => "123"
            ));

            $curl = curl_init();

            curl_setopt($curl, CURLOPT_URL, "https://sandbox.hsys.dev.br/entrada-app-api/auth");
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $params);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type:application/json'));

            $responseBody = curl_exec($curl);
            $err = curl_error($curl);
            curl_close($curl);

            if ($err) {
                return $err;
            } else {
                return json_decode($responseBody);
            }
        } catch (Exception $ex) {
            return $ex;
        }
    }
}

?>