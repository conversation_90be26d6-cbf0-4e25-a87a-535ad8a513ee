<?php

    require_once '../../config/ProjectStage.php';
    require_once '../../config/Auth.php';
    
    $auth = new Auth();
    $resultAuth =  $auth->askAuth();
    $endpoint = ProjectStage::endpoint() . "/data-especial";
    $curl = curl_init();
    
    curl_setopt($curl, CURLOPT_URL, $endpoint);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HTTPHEADER, array('Authorization: Bearer ' . $resultAuth->token));
    curl_setopt($curl, CURLOPT_HTTPGET, 1);
    
    $response_body = curl_exec($curl);
    $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    if ($http_status == 200) {
        echo json_encode(array(
            "status" => 200,
            "result" => json_decode($response_body)
        ));
    } else {
        echo json_encode(array(
            "status" => $http_status,
            "result" => "Ocorreu um erro, tente novamente mais tarde."
        ));
    }

?>