$(window).on("load", function () {
  const state = StepperManager.getState();

  if (!state || !state.data || !state.carrinho || !state.carrinho.length) {
    window.location.href = "../data";
  } else {
    let quantidadeTotal = 0;

    document.getElementById("data").innerHTML = formatarData(state.data);
    document.getElementById("atrativo").innerHTML = state.carrinho[0].nome;

    if (
      state.carrinho[0].produtos &&
      state.carrinho[0].produtos.length > 0 &&
      state.carrinho[0].produtos[0].experiencias
    ) {
      const experiencias = state.carrinho[0].produtos[0].experiencias;

      for (let i = 0; i < experiencias.length; i++) {
        const exp = experiencias[i];
        document.getElementById("preco").innerHTML += `
					<div class='d-flex align-items-center gap-2'>
						<span class='fs-16 fw-400 green-50 produto-info-label'>${exp.nome}:</span>
						<span class='fs-16 fw-600 green-50 produto-info-label'>${exp.quantidade}</span>
					</div>`;
        quantidadeTotal += exp.quantidade;
      }
    }

    requestHorarios(state, quantidadeTotal);
  }
});

function formatarData(dataString) {
  const [ano, mes, dia] = dataString.split("-");
  const data = new Date(ano, mes - 1, dia);
  const diasSemana = [
    "Domingo",
    "Segunda-feira",
    "Terça-feira",
    "Quarta-feira",
    "Quinta-feira",
    "Sexta-feira",
    "Sábado",
  ];
  const diaSemana = diasSemana[data.getDay()];
  return `${diaSemana}, ${dia}/${mes}/${ano}`;
}

function selecionarHorario(idHorario) {
  StepperManager.saveHorario(idHorario);

  const state = StepperManager.getState();

  if (state.carrinho[0].hasAdicional) {
    window.location.href = "../adicional";
  } else {
    window.location.href = "../carrinho";
  }
}

function requestHorarios(state, quantidadeTotal) {
  jQuery.ajax({
    type: "POST",
    url: "AjaxHorarios.php",
    data:
      "atrativo=" +
      state.carrinho[0].id_atrativo +
      "&data=" +
      state.data +
      "&quantidade=" +
      quantidadeTotal,
    cache: false,
    async: false,
    success: function (html) {
      if (html.length > 0) {
        document.getElementById("horarioList").innerHTML = html;
      } else {
        if (state.carrinho[0].hasAdicional) {
          window.location.href = "../adicional";
        } else {
          window.location.href = "../carrinho";
        }
      }
    },
    error: function () {
      window.location.href = "../../erro";
    },
  });
}
