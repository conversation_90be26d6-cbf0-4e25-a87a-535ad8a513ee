<?php include_once '../header.php'; ?>

<?php
    $project_root_path = dirname(dirname(__DIR__));
    $document_root = $_SERVER['DOCUMENT_ROOT'];

    $project_root_path = str_replace('\\', '/', $project_root_path);
    $document_root = str_replace('\\', '/', $document_root);

    $base_path = str_replace($document_root, '', $project_root_path);

    $base_path = rtrim($base_path, '/') . '/';
?>
<script>
    window.BASE_URL = '<?php echo $base_path; ?>';
</script>

<script src="js/script-vDev.js"></script>
<script src="../../assets/js/stepper.js"></script>
<script src="../../assets/js/purchase-stepper.js"></script>
<script src="../carrinho/js/script-vDev.js"></script>
<script src="../cadastro/js/script-pagbank.js"></script>

<div class="reserva-label-container">
	<div class="d-flex mb-2">
    	<a href="../data" class="btn-outlined-custom fw-500">
    		<i class="bi bi-chevron-left me-1"></i> Escolher outro produto
    	</a>
	</div>

	<div class="produto-card">
		<div class="produto-info ">
			<span class="total-label">Total:</span>
			<span class="total-value" id="valor-total">R$ 0,00</span>
		</div>
	</div>
</div>

<div class="purchase-stepper-container">
	<div id="purchase-stepper"></div>
</div>

<div id="loading" class="text-center mt-3 d-none">
	<div class="spinner-border" role="status">
		<span class="visually-hidden">Carregando...</span>
	</div>
</div>


<div>
	<div id="etapa-cpf">
		<div class="campo-cpf">
			<label class="fw-600 fs-16 green-50">Informe seu CPF ou documento</label>
			<div class="input-container-identificacao">
				<div class="input-frame">
					<input type="text" id="pressed" name="cpf" class="input-identificacao cpf fs-12 fw-400 green-50 bg-beige-50"
						   placeholder="000.000.000-00" required="required" inputmode="numeric">
				</div>
			</div>
		</div>

		<div class="d-flex flex-column align-items-end mt-12">
			<button type="button" id="btn-continuar-cpf" class="btn-default fs-18 fw-500">
				<span class="btn-text">CONTINUAR</span>
			</button>
		</div>
	</div>

	<form id="formAcessar" class="d-none">
		<div class="campos-identificacao">
			<div class="campo-cpf">
				<label class="fw-600 fs-16 green-50">CPF ou documento</label>
				<div class="input-container-identificacao">
					<div class="input-frame">
						<input type="text" id="cpf-readonly" name="cpf" class="input-identificacao bg-beige-50 fs-12 fw-400" readonly>
					</div>
				</div>
			</div>

			<div class="campo-senha">
				<label class="fw-600 fs-16 green-50">Senha</label>
				<div class="input-container-identificacao">
					<div class="input-frame">
						<input type="password" name="senha" class="input-identificacao bg-beige-50 fs-12 fw-400 green-50"
							   placeholder="Digite sua senha" required="required">
					</div>
				</div>

				<div id="msg-erro" class="msg-erro"></div>
				<div id="msgSuccessRecuperarSenha" class="msg-sucesso"></div>

				<div class="links-identificacao">
					<a href="#" class="fs-14 green-60 text-decoration-none" data-bs-toggle="modal" data-bs-target="#modalRecuperarSenha">
						Esqueceu a senha? <span class="fw-600 text-decoration-underline">Recuperar agora.</span>
					</a>
					<a href="../cadastro" class="fs-14 green-60 text-decoration-none">
						Não tem acesso? <span class="fw-600 text-decoration-underline">Cadastra-se</span>
					</a>
				</div>
			</div>
		</div>
		
		<hr class="beige-50">

		<div class="d-flex flex-column align-items-end mt-12">
			<button type="submit" name="enviar" class="btn-default fs-18 fw-500">
				SEGUIR PAGAMENTO COM
				<img src="../../assets/images/pagbank_icon_white.png" alt="PagBank" class="pagbank-icon">
			</button>

			<a href="../data" class="btn-cancelar mt-12 fs-18 fw-500">
				CANCELAR
			</a>
		</div>
	</form>
</div>

<div class="modal fade" id="modalRecuperarSenha" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-dialog-centered modal-md">
		<div class="modal-content modal-gastronomia-content">
			<div class="modal-header">
				<span class="fs-18 fw-700 green-40 uppercase" id="exampleModalLabel">Esqueceu sua senha?</span>
				<button type="button" class="btn-close modal-gastronomia-close" data-bs-dismiss="modal" aria-label="Fechar">
					<i class="bi bi-x-lg"></i>
				</button>
			</div>

			<div class="modal-body ">
				<p class="fs-14 fw-400 green-30">
					Confirme seu CPF abaixo e clique em enviar. Enviaremos um e-mail com link para recadastrar sua senha.
				</p>

				<form id="formRecuperarSenha" method="post" class="mt-4">
					<label class="fw-600 fs-16 green-50 mb-1"> CPF </label>
					<div class="input-container-identificacao">
						<div class="input-frame">
							<input type="text" name="cpfRecuperarSenha" class="input-identificacao bg-beige-50 fs-12 fw-400 green-50"
								placeholder="Informe seu CPF" required="required">
						</div>
					</div>

					<hr class="divider-green-50 mt-3 mb-3">
					<div class="text-end">
						<button type="button" class="btn btn-light btn-lg radius-24" data-bs-dismiss="modal"> CANCELAR </button>
						<input type="submit" name="enviarRecuperarSenha" value="ENVIAR" class="btn btn-success btn-lg radius-24">
					</div>
				</form>


			</div>
		</div>
	</div>
</div>

<?php include_once '../footer.php'; ?>