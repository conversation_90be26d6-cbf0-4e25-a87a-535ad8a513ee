<?php

class PagBankService {
    private $config;
    private $baseUrl;

    public function __construct($config) {
        $this->config = $config;
        $this->baseUrl = $config['api_url'] . '/checkouts';
    }

    public function criarCheckout($data) {
        $ch = curl_init($this->baseUrl);
        
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $this->getHeaders(),
            CURLOPT_SSL_VERIFYPEER => false 
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        return [
            'http_code' => $httpCode,
            'data' => json_decode($response, true),
            'curl_error' => $error,
            'raw_response' => $response
        ];
    }

    private function getHeaders() {
        return [
            'Authorization: Bearer ' . $this->config['token'],
            'Content-Type: application/json',
            'x-api-version: 4.0',
            'Accept: application/json'
        ];
    }
} 