$(window).on("load", function () {
  const state = StepperManager.getState();

  if (!state.data) {
    window.location.href = "../data";
  } else {
    request(state.data);
  }
});

function formatarData(dataString) {
  const [ano, mes, dia] = dataString.split("-");
  return `${dia}/${mes}/${ano}`;
}

function request(data) {
  jQuery.ajax({
    type: "POST",
    url: "AjaxAtrativos.php",
    data: "data=" + data,
    cache: false,
    async: false,
    success: function (html) {
      document.getElementById("atrativos").innerHTML = html;
    },
    error: function () {
      window.location.href = "../../erro";
    },
  });
}

function select(id) {
  const botao = document.getElementById("btn-" + id);
  const atrativo = JSON.parse(botao.dataset.objeto);

  StepperManager.saveAtrativo({
    id: atrativo.id,
    nome: atrativo.nome,
    descricao: atrativo.descricao,
    hasAdicional: atrativo.hasAdicional,
    imagem: atrativo.imagem,
    abertura: atrativo.abertura,
    fechamento: atrativo.fechamento,
    informacao: atrativo.informacao,
    ...atrativo,
  });

  window.location.href = "../quantidade";
}

function preencherModal(atrativoData) {
  try {
    const atrativo = JSON.parse(atrativoData);
    
    const modalImagem = document.getElementById('modalAtrativoImagem');
    const modalNome = document.getElementById('modalAtrativoNome');
    const modalDescricao = document.getElementById('modalAtrativoDescricao');
    const modalHorarios = document.getElementById('modalAtrativoHorarios');
    const modalInfoTexto = document.getElementById('modalAtrativoInfoTexto');
    
    if (!modalImagem || !modalNome || !modalDescricao || !modalHorarios || !modalInfoTexto) {
      console.error('❌ Alguns elementos do modal não foram encontrados!');
      return;
    }
    
    modalImagem.src = atrativo.imagem || '';
    modalImagem.alt = atrativo.nome || '';
    modalNome.textContent = atrativo.nome || '';
    modalDescricao.textContent = atrativo.descricao || '';
    modalHorarios.textContent = `Funcionamento: ${atrativo.abertura || ''} às ${atrativo.fechamento || ''}`;
    
    if (atrativo.informacao && atrativo.informacao.trim()) {
      modalInfoTexto.innerHTML = atrativo.informacao.replace(/\n/g, '<br>');
    } else {
      modalInfoTexto.innerHTML = 'Informações sobre este atrativo em breve.';
    }
    
  } catch (error) {
    console.error('❌ Erro ao preencher modal:', error);
  }
}

document.addEventListener('click', function(event) {
  const button = event.target.closest('[data-bs-toggle="modal"][data-bs-target="#modalAtrativoInformacao"]');
  
  if (button) {
    const atrativoData = button.getAttribute('data-bs-atrativo');
    
    if (atrativoData) {
      preencherModal(atrativoData);
    }
  }
});