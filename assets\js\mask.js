$(document).ready(function() {
    $('.cnpj').mask('99.999.999/9999-99');
    $('.cep').mask('99.999-999');
	
	addMaskCpf();
    addMaskTelefone();
});

function addMaskTelefone() {
	
	var mascara = function(value) {
        return value.replace(/\D/g, '').length === 11 ? '(00) 00000-0000' : '(00) 0000-00009';
    },
    options = {
        onKeyPress : function(value, e, field, options) {
            field.mask(mascara.apply({}, arguments), options);
        }
    };

    $('.telefone').mask(mascara, options);
}

function removeMaskTelefone() {
	$(".telefone").unmask();
}

function addMaskCpf() {
	$('.cpf').mask('999.999.999-99');
}

function removeMaskCpf() {
	$(".cpf").unmask();
}