<?php

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

function retornarErro($message) {
    echo json_encode(['success' => false, 'error' => $message]);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('HTTP/1.1 200 OK');
    exit();
}

if (isset($_GET['action']) && $_GET['action'] === 'processar_pagamento') {
    try {
        if (!file_exists(__DIR__ . '/config/pagbankConfig.php')) {
            retornarErro('Arquivo de configuração não encontrado');
        }
        
        if (!file_exists(__DIR__ . '/src/services/PagBankService.php')) {
            retornarErro('Serviço PagBank não encontrado');
        }
        
        if (!file_exists(__DIR__ . '/src/controllers/PagamentoController.php')) {
            retornarErro('Controller de pagamento não encontrado');
        }
        
        require_once __DIR__ . '/config/pagbankConfig.php';
        require_once __DIR__ . '/src/services/PagBankService.php';
        require_once __DIR__ . '/src/controllers/PagamentoController.php';
        
        if (!isset($config)) {
            retornarErro('Configuração não carregada');
        }
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            retornarErro('Método HTTP não permitido. Use POST.');
        }
        
        $precos = [
            'adulto' => 75.00,
            'crianca' => 37.00,
            'idoso' => 37.50,
            'entrada_unica' => 25.00
        ];
        
        $pagamentoController = new PagamentoController($config, $precos);
        $resultado = $pagamentoController->processarPagamento();
        
        echo json_encode($resultado);
        exit;
        
    } catch (Exception $e) {
        retornarErro($e->getMessage());
    }
} else {
    header('Location: pages/index.php');
    exit;
}