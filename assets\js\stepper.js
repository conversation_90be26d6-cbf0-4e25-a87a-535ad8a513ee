const STORAGE_KEY = "RESERVA_APP_CARRINHO";

const steps = [
  { id: 1, label: "Data", route: "data" },
  { id: 2, label: "Atração", route: "atrativo" },
  { id: 3, label: "Ingressos", route: "quantidade" },
  { id: 4, label: "Hor<PERSON><PERSON>", route: "horarios" },
  { id: 5, label: "Carrin<PERSON>", route: "carrinho" },
];

const StepperManager = {
  getState() {
    return JSON.parse(
      localStorage.getItem(STORAGE_KEY) ||
        '{"data": null, "cliente": null, "carrinho": []}'
    );
  },

  saveState(state) {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
    this.renderStepper();
  },

  getCurrentStep() {
    const state = this.getState();
    if (!state.data) return 1;
    if (!state.carrinho || !state.carrinho.length) return 2;
    if (
      !state.carrinho[0] ||
      !state.carrinho[0].produtos ||
      !state.carrinho[0].produtos.length
    )
      return 3;
    if (!state.horario) return 4;
    return 5;
  },

  getCompletedSteps() {
    const state = this.getState();
    const currentStep = this.getCurrentStep();
    const completed = [];

    if (currentStep > 1 && state.data) completed.push(1);
    if (currentStep > 2 && state.carrinho && state.carrinho.length)
      completed.push(2);
    if (
      currentStep > 3 &&
      state.carrinho[0] &&
      state.carrinho[0].produtos &&
      state.carrinho[0].produtos.length
    )
      completed.push(3);
    if (currentStep > 4 && state.horario) completed.push(4);

    return completed;
  },

  saveData(data) {
    const state = this.getState();
    state.data = data;
    this.saveState(state);
  },

  saveAtrativo(atrativo) {
    const state = this.getState();
    state.carrinho = [
      {
        id_atrativo: atrativo.id,
        nome: atrativo.nome,
        produtos: [],
      },
    ];
    this.saveState(state);
  },

  saveProdutos(produtos) {
    const state = this.getState();
    if (state.carrinho && state.carrinho.length > 0) {
      state.carrinho[0].produtos = produtos;
    }
    this.saveState(state);
  },

  saveHorario(idHorario) {
	const state = this.getState();
    if (state.carrinho && state.carrinho.length > 0) {
      state.carrinho[0].horario = idHorario;
    }
    this.saveState(state);
  },

  renderStepper() {
    const stepper = document.getElementById("stepper");
    if (!stepper) return;

    const currentStep = this.getCurrentStep();
    const completedSteps = this.getCompletedSteps();

    stepper.className = "stepper-abs";
    stepper.innerHTML = "";

    const circlesDiv = document.createElement("div");
    circlesDiv.className = "stepper-circles";

    steps.forEach((step, idx) => {
      if (idx > 0) {
        const line = document.createElement("div");
        const isNextStepIncomplete = step.id === currentStep + 1;
        line.className = `stepper-line${
          isNextStepIncomplete ? " stepper-line-dashed" : ""
        }`;
        circlesDiv.appendChild(line);
      }

      const circle = document.createElement("div");
      if (completedSteps.includes(step.id)) {
        circle.className = "step-circle completed";
      } else if (step.id === currentStep) {
        circle.className = "step-circle active";
      } else {
        circle.className = "step-circle upcoming";
      }
      circle.textContent = step.id;
      circlesDiv.appendChild(circle);
    });

    const labelsDiv = document.createElement("div");
    labelsDiv.className = "stepper-labels";

    steps.forEach((step) => {
      const label = document.createElement("div");
      label.className = "step-label";
      label.textContent = step.label;
      if (completedSteps.includes(step.id) || step.id === currentStep) {
        label.style.color = "#2F4634";
      }
      labelsDiv.appendChild(label);
    });

    stepper.appendChild(circlesDiv);
    stepper.appendChild(labelsDiv);
  },
};

// Inicialização
document.addEventListener("DOMContentLoaded", () =>
  StepperManager.renderStepper()
);
