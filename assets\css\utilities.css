@import "variables.css";

/* cores */
.green-60 {
  color: var(--color-green-60);
}

.green-50 {
  color: var(--color-green-50);
}

.green-40 {
  color: var(--color-green-40);
}

.green-30 {
  color: var(--color-green-30);
}

.bg-green-50 {
  background-color: var(--color-green-50);
}

.burgundy-50 {
  color: var(--color-burgundy-50);
}

.gray-50 {
  color: var(--color-gray-50);
}

/* background */
.bg-orange-50 {
  background-color: var(--color-orange-50);
}

.bg-beige-50 {
  background-color: var(--color-beige-50);
}

.bg-beige-40 {
  background-color: var(--color-beige-40);
}

.bg-beige-30 {
  background-color: var(--color-beige-30);
}

.bg-beige-20 {
  background-color: var(--color-beige-20);
}

.beige-10 {
  color: var(--color-beige-10);
}

.beige-50 {
  color: var(--color-beige-50);
}

.beige-20 {
  color: var(--color-beige-20);
}

/* border radius */
.radius-12 {
  border-radius: var(--radius-12);
}

.radius-18 {
  border-radius: var(--radius-18);
}

.radius-20 {
  border-radius: var(--radius-20);
}

.radius-24 {
  border-radius: var(--radius-24);
}

.radius-30 {
  border-radius: var(--radius-30);
}

.radius-32 {
  border-radius: var(--radius-32);
}

.radius-44 {
  border-radius: var(--radius-44);
}

/* padding */
.padding-8-12 {
  padding: var(--spacing-8) var(--spacing-12);
}

.padding-10-14 {
  padding: var(--spacing-10) var(--spacing-14);
}

.padding-6-20 {
  padding: var(--spacing-6) var(--spacing-20);
}

.padding-14 {
  padding: var(--spacing-14);
}

.padding-32-20 {
  padding: var(--spacing-32) var(--spacing-20);
}

.padding-12-20 {
  padding: var(--spacing-12) var(--spacing-20);
}

/* margin */
.mb-custom-1-25 {
  margin-bottom: 1.25rem;
}

.mb-custom-0-5 {
  margin-bottom: 0.5rem;
}

.mb-16 {
  margin-bottom: var(--spacing-16);
}

.mb-20 {
  margin-bottom: var(--spacing-20);
}

.mt-20 {
  margin-top: var(--spacing-20);
}

.mt-12 {
  margin-top: var(--spacing-12);
}

.gap-12 {
  gap: var(--spacing-12);
}

.gap-10 {
  gap: var(--spacing-10);
}

.mt-custom-12 {
  margin-top: 12px;
}

/* modal */
.modal-lg {
  max-width: 615px;
}

/* divider */
.divider-custom {
  margin-bottom: 32px;
}

/*tamanho*/
.w-h-32 {
  width: var(--width-32);
  height: var(--height-32);
}

.position-relative {
  position: relative;
}

.position-absolute {
  position: absolute;
}

.text-uppercase {
  text-transform: uppercase;
}

.border-green-60 {
  border-color: var(--color-green-60) !important;
}