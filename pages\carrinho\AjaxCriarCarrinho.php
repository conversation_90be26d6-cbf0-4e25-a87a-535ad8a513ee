<?php
require_once '../../config/ProjectStage.php';
require_once '../../config/Auth.php';

try {
    if (!isset($_POST['carrinho'])) {
        echo json_encode(array("status" => 400, "message" => "Dados do carrinho não fornecidos"));
        exit;
    }

    $carrinho = json_decode($_POST['carrinho']);
    
    if (!$carrinho) {
        echo json_encode(array("status" => 400, "message" => "Dados do carrinho inválidos"));
        exit;
    }
    
    $auth = new Auth();
    $resultAuth = $auth->askAuth();
    $header = array('Authorization: Bearer ' . $resultAuth->token, "Content-Type: application/json");
    $curl = curl_init();
    
    curl_setopt($curl, CURLOPT_URL, ProjectStage::endpoint() . "/carrinho");
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
    curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($carrinho));
    
    curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, true);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 2);
    
    $curl_response = curl_exec($curl);
    $response_body = json_decode($curl_response);
    $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    
    curl_close($curl);
    
    if ($http_status == 200) {
        echo json_encode(array("status" => $http_status, "data" => $response_body));
    } else {
        $errorMessage = "Erro ao criar carrinho (Status: $http_status)";
        if ($response_body && isset($response_body->mensagem)) {
            $errorMessage = $response_body->mensagem;
        } elseif ($response_body && isset($response_body->message)) {
            $errorMessage = $response_body->message;
        } elseif ($curl_response) {
            $errorMessage = "Erro da API: " . $curl_response;
        }
        
        echo json_encode(array(
            "status" => $http_status,
            "message" => $errorMessage
        ));
    }
    
} catch (Exception $e) {
    echo json_encode(array("status" => 500, "message" => "Erro interno: " . $e->getMessage()));
}
?>
