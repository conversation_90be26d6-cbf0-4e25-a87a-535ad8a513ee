$(window).on("load", function () {
  	const state = StepperManager.getState();

  	if (!state.data) {
		window.location.href = "../data";
	} else {
		document.getElementById("data").innerHTML = formatarData(state.data);
		document.getElementById("atrativo").innerHTML = state.carrinho[0].nome;

    	requestProduto(state);
	}
});

function formatarData(dataString) {
	const [ano, mes, dia] = dataString.split("-");
	return `${dia}/${mes}/${ano}`;
}

$(window).on("load", function () {
	document.getElementById("formQuantidade").addEventListener("submit", function (event) {
		
      event.preventDefault();

      let inputs = document.getElementsByName("idPreco");
      let quantidadeTotal = 0;
      let experiencias = [];

      for (let i = 0; i < inputs.length; i++) {
        let quantidade = parseInt(document.getElementsByName("quantidade")[i].value) || 0;
        let idPreco = document.getElementsByName("idPreco")[i].value;
        let nome = document.getElementsByName("nomePreco")[i].value;
        let precoUnitario = parseFloat(
          document.getElementsByName("precoUnitario")[i].value
        );

        experiencias.push({
          id: parseInt(idPreco),
          nome: nome,
          quantidade: quantidade,
          preco_unitario: precoUnitario,
        });

        quantidadeTotal += quantidade;
      }

      if (quantidadeTotal == 0) {
        document.getElementById("msg-erro").innerHTML =
          " * Informe a quantidade de pessoas";
      } else {
        const produtos = [
          {
            id: 1,
            experiencias: experiencias,
          },
        ];

        StepperManager.saveProdutos(produtos);

        window.location.href = "../horarios";
      }
    });
});

function requestProduto(state) {
	
	const xhr = new XMLHttpRequest();
	const url = "AjaxProduto.php";
	const params = "atrativo=" + state.carrinho[0].id_atrativo + "&data=" + state.data;

	xhr.open("POST", url, false); // 'false' para requisição síncrona, não recomendado para UX
	xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");

	xhr.onload = function() {
    	if (xhr.status === 200) {
      		document.getElementById("produtoList").innerHTML = xhr.responseText;
    	} else {
	      	//window.location.href = "../../erro";
	    }
  	};

  	xhr.onerror = function() {
	    //window.location.href = "../../erro";
  	};

  	xhr.send(params);
}

function sum(key) {
  let quantidade = document.getElementsByName("quantidade")[key].value;
  quantidade = parseInt(quantidade) + 1;

  if (quantidade > 0) {
    document.getElementsByName("quantidade")[key].value = quantidade;
  } else {
    document.getElementsByName("quantidade")[key].value = 1;
  }
  calcular(key);
}

function sub(key) {
  let quantidade = document.getElementsByName("quantidade")[key].value;
  quantidade = parseInt(quantidade) - 1;

  if (quantidade > 0) {
    document.getElementsByName("quantidade")[key].value = quantidade;
  } else {
    document.getElementsByName("quantidade")[key].value = 0;
  }
  calcular(key);
}

function calcular(key) {
  let preco =
    document.getElementsByName("quantidade")[key].value *
    document.getElementsByName("precoUnitario")[key].value;
  document.getElementById("precoFinal" + key).innerHTML = preco
    .toFixed(2)
    .replace(".", ",");

  var inputs = document.getElementsByName("idPreco");
  let total = 0;

  for (let i = 0; i < inputs.length; i++) {
    if (document.getElementsByName("quantidade")[i].value > 0) {
      let preco =
        document.getElementsByName("quantidade")[i].value *
        document.getElementsByName("precoUnitario")[i].value;
      total += preco;
    }
  }
  document.getElementById("total").innerHTML = total
    .toFixed(2)
    .replace(".", ",");
}
