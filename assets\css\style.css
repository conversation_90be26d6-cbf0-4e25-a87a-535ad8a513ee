/* geral */
* {
	font-family: 'Poppins-Regular';
}
body {
	margin: 0;
    padding: 0;
    box-sizing: border-box;
    background-size: cover;
    color: #1F2D1B !important;
	font-size: 18px !important;
	background-position: center;
	background-repeat: no-repeat;
	background-color: rgb(255,254,240) !important;
    background-image: url('../images/background.webp');
}
h1, h2, h3, h4, h5, h6, p {
	margin-bottom: 0;
}
.footer .container {
   padding-top: 3rem;
   padding-bottom: 3rem;
 }

@media (max-width: 771px) {
	body {
		background-color: rgb(235,222,181) !important;
	}
}

/* card */
.card-main {
  	border: 0;
  	padding: 32px;
  	max-width: 670px;
  	border-radius: 32px;
  	margin: 8% auto;
  	background-color: rgb(235,222,181);
}
.card-main > .card-body {
	padding: 0 !important;
}

@media (max-width: 771px) {
	.card-main {
		margin: 0 !important;
		max-width: unset !important;
		padding: 32px 15px;
		border-radius: 0 !important;
		box-shadow: none !important;
	}
}

.text-quaternary {
	color: #777777 !important;
}
.legenda-fechado {
	width: 35px;
	height: 35px;
	display: -webkit-inline-box;
	background-color: #ccc;
}
.legenda-durante-semana {
	width: 35px;
	height: 35px;
	line-height: 15px;
	display: -webkit-inline-box;
	background-color: rgb(42,50,32);
}
.legenda-final-semana {
	width: 35px;
  	height: 35px;
	display: -webkit-inline-box;
	background-color: rgb(138,46,54);
}
.col-legenda {
	top: 3px;
	position: relative;
	line-height: 15px;
}
.btn {
	font-family: 'Poppins-Regular';
}
.btn-lg {
	padding-top: 0.8rem;
    padding-bottom: 0.8rem;
	font-size: 14px !important;
}
.bg-secondary {
	background-color: #384A36 !important;
	border-color: #384A36 !important;
}
.bg-tertiary {
	background-color: #FFAC33 !important;
	border-color: #FFAC33 !important;
}
.bg-tertiary-border {
	color: #FFAC33 !important;
	background-color: transparent;
	border-color: #FFAC33 !important;
}
.bg-quaternary {
	color: #8A2E36;
	border-color: #8A2E36;
}
.bg-quaternary-border {
	color: #8A2E36;
	font-family: 'Poppins-Medium';
	background-color: transparent;
	border-color: #8A2E36;
}
.bg-quaternary-border:hover {
	border-color: #8A2E36;
	color: #8A2E36;
}

.fs-6 {
	font-size: 14px !important;
}
.fs-7 {
	font-size: 12px !important;
}
.text-tertiary {
	color: #888888 !important;
}
.text-one-line {
	display: -webkit-box;
  	-webkit-line-clamp: 1;
  	overflow: hidden;
  	-webkit-box-orient: vertical;
}
.atrativo-arrow {
	width: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}
#atrativoInformacao p {
	margin-bottom: 1rem !important;
}




/* NÃO REVISADO */

h6 {
	margin-bottom: 0 !important;
}



.cursor-pointer {
	cursor: pointer;
}
.msg-alerta {
	padding: 5px 10px;
	width: max-content;
	font-family: 'Poppins-SemiBold';
	background-color: RGB(255,172,51) !important;
}
.msg-finalizado {
	padding: 5px 10px;
	color: #fff;
	width: max-content;
	font-family: 'Poppins-SemiBold';
	background-color: rgb(56,74,54) !important;
}
.step.finish {
    background-color: #198754;
    opacity: 1;
}
.step.active {
    opacity: 1;
}
.step {
    height: 15px;
    width: 15px;
    margin: 0 2px;
    background-color: #999;
    border: none;
    border-radius: 50%;
    display: inline-block;
    opacity: 0.5;
}
.dropdown-menu-custom {
	max-height: 250px;
	overflow-y: overlay;
}

@-webkit-keyframes pulse {
	to {
		box-shadow: 0 0 0 15px rgba(232, 76, 61, 0);
	}
}

@-moz-keyframes pulse {
	to {
		box-shadow: 0 0 0 15px rgba(232, 76, 61, 0);
	}
}

@-ms-keyframes pulse {
	to {
		box-shadow: 0 0 0 15px rgba(232, 76, 61, 0);
	}
}

@keyframes pulse {
	to {
		box-shadow: 0 0 0 15px rgba(232, 76, 61, 0);
	}
}

/* HORARIOS */
.btn-group-vertical > .btn:not(:first-child) {
	margin-top: initial;
}
.btn-horario-1 {
	padding: .375rem .75rem;
	font-size: 16px !important;
}
.btn-horario-2 {
	padding: .375rem .75rem;
	font-size: 16px !important;
	border-bottom-left-radius: 5px;
	border-bottom-right-radius: 5px;
	font-family: 'Poppins-SemiBold';
}
.btn-lotado {
	border-width: 2px;
	border-color: #6c757d;
}
.btn-lotado:hover {
	border-color: #6c757d;
}
.btn-lotado .btn-horario-1 {
	background-color: #6c757d;
	color: #fff;
}
.btn-lotado .btn-horario-2 {
	background-color: #fff;
	color: #6c757d;
}
.btn-horario {
	border-width: 2px;
	border-color: #198754;
	border-radius: 12px;
	overflow: hidden;
}
.btn-horario:hover {
	border-color: #198754 !important;
}
.btn-check:not(:checked) + .btn-horario div:nth-child(1) {
	background-color: #198754;
	color: #fff;
}
.btn-check:not(:checked) + .btn-horario div:nth-child(2) {
	background-color: #fff;
	color: #198754;
}
.btn-check:checked + .btn-horario {
	border: 2px solid rgb(255,172,51) !important;
}
.btn-check:checked + .btn-horario div:nth-child(1) {
	background-color: rgb(255,172,51) !important;
	color: rgb(56,74,54);
	font-family: 'Poppins-SemiBold';
}
.btn-check:checked + .btn-horario div:nth-child(2) {
	background-color: #fff !important;
	color: rgb(56,74,54);
}
.btn-check:focus + .btn-horario {
  	box-shadow: none;
}

/***** SIMPLE AUTOCOMPLETE *****/
div.autocomplete {
	z-index: 1000;
	position: absolute;
	min-width: 100%;
	background-color: #eee;
	border: 2px solid #ccc;
	font-size: 14px;
	font-family: "calibri";
}
div.autocomplete ul {
	padding-left: 0;
	margin: 0;
	list-style: none;
}
div.autocomplete ul li {
	padding: 8px 10px;
	cursor: pointer;
	color: #000;
}
div.autocomplete ul li.sel {
	background-color: #87cefa;
}

/* BOOTSTRAP */

.text-secondary {
	color: rgb(56,74,54) !important;
}

.btn-success {
	background-color: rgb(56,74,54);
	border-color: rgb(56,74,54);
}
.btn-success:hover {
	background-color: rgb(56,74,54);
	border-color: rgb(56,74,54);
}
.btn-secondary {
	background-color: rgb(138,46,54);
	border-color: rgb(138,46,54);
}
.btn-secondary:hover {
	background-color: rgb(138,46,54);
	border-color: rgb(138,46,54);
}


/***** LOADING *****/
.loading {
    position: fixed;
    z-index: 9999;
    height: 2em;
    width: 2em;
    overflow: show;
    margin: auto;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

/* Transparent Overlay */
.loading:before {
    content: '';
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
	opacity: 0.5;
}

/* flatpickr */
.flatpickr-calendar {
	width: auto !important;
	box-shadow: none !important;
	margin-top: 5px !important;
}