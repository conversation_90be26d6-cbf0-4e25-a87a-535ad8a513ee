<?php

    header("Content-Type: application/json");
    session_start();
    
    require_once '../../config/ProjectStage.php';
    require_once '../../config/Auth.php';
    
    $reserva = json_decode($_POST['pedido'], true);
    
    if (empty($reserva[0]['idHorario'])) {
        echo json_encode(array( "success" => false, "message" => ""));
    }
    
    $params = array(
        "idReserva" => isset($reserva[0]['idReserva']) ? $reserva[0]['idReserva'] : null,
        "numeroPessoas" => $reserva[0]['quantidade'],
        "data" => date('Y-m-d', strtotime(str_replace("/", "-", $reserva[0]['data']))),
        "idHorario" => $reserva[0]['idHorario'],
        "idCliente" => $_SESSION['cliente'],
        "status" => "FINALIZADA",
        "atrativos" => $reserva[0]["atrativos"]
    );
    
    $auth = new Auth();
    $resultAuth =  $auth->askAuth();
    $header = array('Authorization: Bearer ' . $resultAuth->token, "Content-Type: application/json");
    $curl = curl_init();
    
    curl_setopt($curl, CURLOPT_URL, ProjectStage::endpoint() . "/reserva");
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
    curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($params));
    
    curl_setopt( $curl, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt( $curl, CURLOPT_SSL_VERIFYPEER, true);
    curl_setopt( $curl, CURLOPT_SSL_VERIFYHOST, 2);
    
    $response_body = json_decode(curl_exec($curl));
    $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    if ($http_status == 200) {
        echo json_encode(array( "status" => 200, "message" => $response_body ));
    } else {
        echo json_encode(array(
            "status" => $http_status,
            "message" => isset($response_body->mensagem) ? $response_body->mensagem : "Ocorreu um erro, tente novamente mais tarde."
        ));
    }
?>