jQuery(window).on("load", function() {
	const state = JSON.parse(localStorage.getItem('RESERVA_APP_CARRINHO') || '{}');
	if (state && state.carrinho && state.carrinho.length > 0) {
		let total = 0;
		state.carrinho.forEach(atrativo => {
			if (atrativo.produtos && atrativo.produtos.length > 0) {
				atrativo.produtos.forEach(produto => {
					if (produto.experiencias) {
						produto.experiencias.forEach(exp => {
							if (exp.quantidade > 0) {
								total += exp.quantidade * exp.preco_unitario;
							}
						});
					}
				});
			}
		});
		document.getElementById("valor-total").textContent = `R$ ${total.toFixed(2).replace('.', ',')}`;
	}
});

jQuery(window).on("load", function() {
	jQuery("#pressed").keypress(function(e) {
		if (e.which === 13) {
			console.log("teste2"); 
			jQuery("#btn-continuar-cpf").click();
		}
	});
	
	jQuery("#btn-continuar-cpf").click(function() {
		const documento = document.getElementById("pressed").value;
		
		if (!documento || documento.length !== 14) {
			alert("Por favor, informe um CPF válido com 11 dígitos.");
			document.getElementById("pressed").focus();
			return;
		}
		
		if (document.getElementsByName('cpfRecuperarSenha')[0]) {
			document.getElementsByName('cpfRecuperarSenha')[0].value = documento;
		}
		
		jQuery.ajax({
			type: 'POST',
			dataType: 'json',
			url: "AjaxCliente.php",
			data: "documento=" + documento,
			cache: false,
			beforeSend: function() {
				document.getElementById('loading').classList.remove('d-none');
				jQuery("#btn-continuar-cpf").prop('disabled', true);
				jQuery("#btn-continuar-cpf .btn-text").text('VERIFICANDO...');
			},
			complete: function(response) {
				document.getElementById('loading').classList.add('d-none');
				jQuery("#btn-continuar-cpf").prop('disabled', false);
				jQuery("#btn-continuar-cpf .btn-text").text('CONTINUAR');
				
				// Verifica se o CPF existe no sistema
				if (response.responseJSON && response.responseJSON.status === 200) {
					// CPF existe - libera formulário de senha
					liberarFormularioCompleto(documento);
				} else {
					// CPF não existe - redireciona para cadastro
					// Salva o CPF no localStorage para pré-preenchimento
					localStorage.setItem('RESERVA_APP_DOCUMENTO_PREENCHIMENTO', documento);
					window.location.href = "../cadastro";
				}
			}
		});
	});
});

function liberarFormularioCompleto(cpf) {
	document.getElementById("etapa-cpf").classList.add('d-none');
	document.getElementById("formAcessar").classList.remove('d-none');
	document.getElementById("cpf-readonly").value = cpf;
	
	// Foca no campo senha já que o CPF existe
	document.getElementsByName('senha')[0].focus();
}

jQuery(window).on("load", function() {
	jQuery('#formAcessar').submit(function(e) {
		e.preventDefault();
		
		const documento = document.getElementById("cpf-readonly").value;
		const senha = document.getElementsByName('senha')[0].value;
		
		jQuery.ajax({
			type: 'POST',
			dataType: 'json',
			url: "AjaxAcessar.php",
			data: {
				documento: documento,
				senha: senha
			},
			cache: false,
			beforeSend: function() {
				const btnEnviar = document.getElementsByName('enviar')[0];
				btnEnviar.disabled = true;
			},
			complete: function(response) {
				const btnEnviar = document.getElementsByName('enviar')[0];
				
				if (response.responseJSON && response.responseJSON.status == 200) {
					let carrinho = JSON.parse(localStorage.getItem('RESERVA_APP_CARRINHO'));
					// Se carrinho é array, pega o primeiro elemento
					if (Array.isArray(carrinho)) {
						carrinho = carrinho[0];
					}
					carrinho.cliente = response.responseJSON.message;
					localStorage.setItem('RESERVA_APP_CARRINHO', JSON.stringify(carrinho));

					criarCarrinho(carrinho);

					// Ir direto para pagamento
					if (typeof iniciarPagamentoPagBank === 'function') {
						iniciarPagamentoPagBank();
					} else {
						// Se não tiver a função, vai para carrinho
						window.location.href = "../carrinho";
					}
				} else if (response.responseJSON && response.responseJSON.status !== 0) {
					document.getElementById("msg-erro").innerHTML = response.responseJSON.message;
					document.getElementById("msg-erro").style.display = "block";
				} else {
					window.location.href = "../erro";
				}
				
				btnEnviar.disabled = false;
				const btnText = btnEnviar.querySelector('.fs-18');
				if (btnText) {
					btnText.textContent = "SEGUIR PAGAMENTO COM";
				}
			}
		});
	});
});

jQuery(window).on("load", function() {
	jQuery('#formRecuperarSenha').submit(function() {
	    jQuery.ajax({
	        type: 'POST',
	        dataType: 'json',
	        url: "apiRecuperarSenha.php",
	        data: jQuery(this).serialize(),
	        cache: false,
	        beforeSend: function() {
	    		const btnEnviar = document.getElementsByName('enviarRecuperarSenha')[0];
	    		btnEnviar.disabled = true;
	    		btnEnviar.value = "ENVIANDO...";
	     	},
	        complete: function(response) {
	        	const btnEnviar = document.getElementsByName('enviarRecuperarSenha')[0];
	        	
				if (response.responseJSON && response.responseJSON.success) {
					document.getElementById("msgSuccessRecuperarSenha").innerHTML = response.responseJSON.message;
					document.getElementById("msgSuccessRecuperarSenha").style.display = "block";
					jQuery('#modalRecuperarSenha').modal('hide');
				} else {
					const message = response.responseJSON && response.responseJSON.message ? 
						response.responseJSON.message : "Erro ao enviar recuperação de senha";
					document.getElementById("msgErroRecuperarSenha").innerHTML = message;
					document.getElementById("msgErroRecuperarSenha").style.display = "block";
				}
				
				btnEnviar.disabled = false;
				btnEnviar.value = "ENVIAR";
	        }
	    });
	    return false;
    });
});

