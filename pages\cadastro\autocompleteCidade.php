<?php
    require_once '../../config/ProjectStage.php';
    require_once '../../config/Auth.php';
    
    // AUTENTICACAO
    $auth = new Auth();
    $resultAuth =  $auth->askAuth();
    $endpoint = ProjectStage::endpoint() . "/cidade?nome=" . urlencode(str_replace(" ", "+", $_POST['query']));
    $curl = curl_init();
    
    curl_setopt($curl, CURLOPT_URL, $endpoint);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HTTPHEADER, array('Authorization: Bearer ' . $resultAuth->token));
    curl_setopt($curl, CURLOPT_HTTPGET, 1);
    
    $response_body = curl_exec($curl);
    curl_close($curl);
    $list = json_decode($response_body);
    
    if (count($list) > 0) {
        echo '<div class="autocomplete">';
        echo '<ul>' . "\n";
        foreach (json_decode($response_body) as $b) {
            $p = $b->nome . " - " . $b->sigla;
            $p = preg_replace('/(' . $_POST['query'] . ')/i', '<span style="font-weight:bold;">$1</span>', $p);
            echo "\t" . '<li onclick="selecionarCidade(' . $b->id . ',\'' . $b->nome . ' - ' . $b->sigla . '\') " >' . ($p) . '</li>' . "\n";
        }
        echo '</ul>';
        echo '</div>';
    }
    
?>