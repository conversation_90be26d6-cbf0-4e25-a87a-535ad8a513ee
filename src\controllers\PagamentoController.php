<?php

class PagamentoController {
    private $pagbank;
    private $precos;
    private $config;

    public function __construct($config, $precos = []) {
        $this->pagbank = new PagBankService($config);
        $this->precos = $precos;
        $this->config = $config;
    }

    public function processarPagamento() {
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);

        if (!$data || !isset($data['cliente']) || !isset($data['carrinho'])) {
            return ['success' => false, 'error' => 'Dados da requisição inválidos ou incompletos.'];
        }

        $cliente = $data['cliente'];
        $carrinho = $data['carrinho'];

        $clienteErros = $this->validarDadosCliente($cliente);
        if (!empty($clienteErros)) {
            return ['success' => false, 'error' => 'Dados do cliente incompletos: ' . implode(', ', $clienteErros)];
        }

        $itens = [];
        $totalGeralEmCentavos = 0;

        foreach ($carrinho as $atrativo) {
            if (isset($atrativo['produtos'])) {
                foreach ($atrativo['produtos'] as $produto) {
                    if (isset($produto['experiencias'])) {
                        foreach ($produto['experiencias'] as $exp) {
                            if (isset($exp['quantidade']) && (int)$exp['quantidade'] > 0) {
                                $precoUnitario = (float)$exp['preco_unitario'];
                                if ($precoUnitario <= 0) {
                                    continue;
                                }
                                $quantidade = (int)$exp['quantidade'];
                                $precoUnitarioEmCentavos = (int) round($precoUnitario * 100);
                                $totalGeralEmCentavos += $quantidade * $precoUnitarioEmCentavos;
                                $itens[] = [
                                    'name' => (string)$exp['nome'],
                                    'quantity' => $quantidade,
                                    'unit_amount' => $precoUnitarioEmCentavos
                                ];
                            }
                        }
                    } elseif (isset($produto['quantidade']) && $produto['quantidade'] > 0) {
                        $precoUnitario = isset($produto['preco_unitario']) ? (float)$produto['preco_unitario'] : ((float)$data['valorTotal'] / (int)$produto['quantidade']);
                        $precoUnitarioEmCentavos = (int) round($precoUnitario * 100);
                        $totalGeralEmCentavos += (int)$produto['quantidade'] * $precoUnitarioEmCentavos;
                        $itens[] = [
                            'name' => $produto['nome'] ?? $atrativo['nome'],
                            'quantity' => (int)$produto['quantidade'],
                            'unit_amount' => $precoUnitarioEmCentavos
                        ];
                    }
                }
            } elseif (!empty($itens) === false && isset($data['valorTotal']) && $data['valorTotal'] > 0) {
                $precoUnitarioEmCentavos = (int) round((float)$data['valorTotal'] * 100);
                $totalGeralEmCentavos = $precoUnitarioEmCentavos;
                $itens[] = [
                    'name' => $atrativo['nome'],
                    'quantity' => 1,
                    'unit_amount' => $precoUnitarioEmCentavos
                ];
            }
        }

        if (empty($itens)) {
            return ['success' => false, 'error' => 'Selecione pelo menos um ingresso no carrinho.'];
        }
    
        $checkoutData = $this->criarDadosCheckout($itens, $totalGeralEmCentavos, $cliente);
        $response = $this->pagbank->criarCheckout($checkoutData);

        if (!in_array($response['http_code'], [200, 201]) || !isset($response['data']['links'])) {
            $errorMsg = 'Erro ao criar checkout';
            if (isset($response['data']['error_messages'])) {
                $errors = array_map(fn($e) => $e['description'], $response['data']['error_messages']);
                $errorMsg .= ': ' . implode(', ', $errors);
            }
            return [
                'success' => false,
                'error' => $errorMsg,
                'details' => $response['data'] ?? 'Sem detalhes',
                'debug' => [
                    'http_code' => $response['http_code'],
                    'curl_error' => $response['curl_error'] ?? null,
                    'raw_response' => $response['raw_response'] ?? null
                ]
            ];
        }

        $payUrl = '';
        foreach ($response['data']['links'] as $link) {
            if ($link['rel'] === 'PAY') {
                $payUrl = $link['href'];
                break;
            }
        }

        if (!$payUrl) {
            return ['success' => false, 'error' => 'URL de pagamento não encontrada na resposta.'];
        }

        return [
            'success' => true,
            'pay_url' => $payUrl,
            'checkout_id' => $response['data']['id']
        ];
    }

    private function criarDadosCheckout($itens, $totalGeral, $cliente) {
        $referenceId = 'FLUSS-' . uniqid();
        
        $nome = $cliente['nome'];
        $sobrenome = $cliente['sobrenome'];
        $documento = preg_replace('/\D/', '', $cliente['documento']);
        
        if (strlen($documento) != 11) {
            throw new Exception('CPF inválido. Deve ter 11 dígitos.');
        }
        
        $telefone = preg_replace('/\D/', '', $cliente['telefone1']);
        $ddi = $cliente['ddi1'];
        
        if (strlen($telefone) < 10) {
            throw new Exception('Telefone inválido. Deve ter pelo menos 10 dígitos.');
        }
        
        if (empty($nome) || empty($sobrenome) || empty($cliente['email']) || !filter_var($cliente['email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Nome, sobrenome e email válido são obrigatórios.');
        }
        
        $areaTelefone = substr($telefone, 0, 2);
        $numeroTelefone = substr($telefone, 2);

        $redirectUrl = $this->config['is_sandbox']
            ? 'https://sandbox.flusshaus.com.br/reserva-finalizada'
            : 'https://flusshaus.com.br/reserva-finalizada';

        $payload = [
            'reference_id' => $referenceId,
            'customer' => [
                'name'     => $nome . ' ' . $sobrenome,
                'email'    => $cliente['email'],
                'tax_id'   => $documento,
                'phones'   => [[
                    'country' => $ddi,
                    'area'    => $areaTelefone,
                    'number'  => $numeroTelefone,
                    'type'    => 'MOBILE'
                ]]
            ],
            'items' => $itens,
            'charges' => [[
                'reference_id' => "charge_{$referenceId}",
                'description'  => 'Compra de Ingressos Flusshaus',
                'amount'       => [
                    'value'    => (int)$totalGeral,
                    'currency' => 'BRL'
                ]
            ]],
            'redirect_url' => $redirectUrl,
            'qr_codes' => [[
                'amount' => ['value' => (int)$totalGeral]
            ]],
        ];

        return $payload;
    }

    private function validarDadosCliente($cliente) {
        $erros = [];
        if (!isset($cliente['nome'])) $erros[] = 'Nome é obrigatório';
        if (!isset($cliente['sobrenome'])) $erros[] = 'Sobrenome é obrigatório';
        if (!isset($cliente['email'])) $erros[] = 'Email é obrigatório';
        if (!isset($cliente['documento'])) $erros[] = 'Documento é obrigatório';
        if (!isset($cliente['telefone1'])) $erros[] = 'Telefone é obrigatório';
        return $erros;
    }
} 