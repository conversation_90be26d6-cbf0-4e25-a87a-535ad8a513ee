<?php

require_once '../../config/ProjectStage.php';
require_once '../../config/Auth.php';

$auth = new Auth();
$resultAuth =  $auth->askAuth();
$endpoint = ProjectStage::endpoint() . "/atrativo/data/" . $_POST['data'];
$curl = curl_init();

curl_setopt($curl, CURLOPT_URL, $endpoint);
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
curl_setopt($curl, CURLOPT_HTTPHEADER, array('Authorization: Bearer ' . $resultAuth->token));
curl_setopt($curl, CURLOPT_HTTPGET, 1);

$response_body = curl_exec($curl);
curl_close($curl);

foreach (json_decode($response_body) as $value) {

	$json = json_encode($value);
?>

	<div class="card overflow-hidden border-0 mt-3 radius-18">
		<div class="row g-0 h-100">
			<div class="col-sm-12 col-md-auto d-flex">
				<img src="<?php echo $value->imagem; ?>" class="img-atrativo" alt="Imagem do atrativo">
			</div>
			
			<div class="col-sm-12 col-md">
				<div class="card-body atrativo-card-body">
					<p class="fw-700 fs-18 uppercase green-40 info-atrativo"> <?php echo $value->nome; ?> </p>
					<p class="fw-400 fs-14 green-30"> <?php echo $value->descricao; ?> </p>
					
					<hr style="margin: 0.7rem 0;" />
					
					<div class="d-flex gap-2">
						<span class="btn btn-sm bg-tertiary radius-24 padding-8-12 green-60 info-atrativo">
							<?php echo str_replace("00", "", str_replace(":", "h", $value->abertura)); ?> às <?php echo str_replace("00", "", str_replace(":", "h", $value->fechamento)); ?>
						</span>
						<span class="btn btn-sm bg-quaternary-border burgundy-50 radius-24 padding-8-12 d-flex gap-1" data-bs-toggle="modal" data-bs-target="#modalAtrativoInformacao" data-bs-atrativo='<?= $json ?>'>
						<i class="bi bi-info-circle"></i>informações e valores
						</span>
					</div>
				
					<div class="col-auto bg-secondary atrativo-arrow green-50">
						<button type="button" class="btn bi bi-chevron-right text-white fs-6" id="btn-<?php echo $value->id; ?>" data-objeto='<?= $json ?>' onclick="select(<?php echo $value->id; ?>)"></button>
					</div>
				</div>
			</div>
		</div>
	</div>

<?php } ?>