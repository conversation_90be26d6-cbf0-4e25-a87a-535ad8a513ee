$( window ).on("load", function() {
	
	const state = StepperManager.getState();
			
	if (!state || !state.data) {
		window.location.href = "../data";
	} else {
		request(state);
	}
});

function request(state) {
	jQuery.ajax({
		type: 'POST',
		url: "AjaxAtrativos.php",
		data: "atrativo=" + state.carrinho[0].id_atrativo + "&data=" + state.data + "&carrinho=" + JSON.stringify(state.carrinho),
		cache: false,
		async: false,
		success: function(html) {
			document.getElementById("atrativos").innerHTML = html;
        },
		error: function() {
			window.location.href = "../../erro";
		}
	});
}

function select(id) {
	
	const botao = document.getElementById('btn-' + id);
	const atrativo = JSON.parse(botao.dataset.objeto);
	const state = StepperManager.getState();
	
	state.carrinho.push({ 
		id_atrativo: atrativo.id, 
		nome: atrativo.nome, 
		hasAdicional: atrativo.hasAdicional, 
		imagem: atrativo.imagem,
		produtos: []
	});
	
	StepperManager.saveState(state);
	
	window.location.href = "../quantidade";
}