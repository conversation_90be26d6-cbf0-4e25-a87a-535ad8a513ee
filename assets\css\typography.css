@import 'variables.css';

@font-face {
    font-family: 'Poppins-Light';
    font-weight: normal;
    font-style: normal;
    font-display: swap;
    unicode-range: U+000-5FF;
    src: local('Poppins-Light'), url('../fonts/Poppins-Light.ttf') format('truetype');
}

@font-face {
    font-family: 'Poppins-Regular';
    font-weight: normal;
    font-style: normal;
    font-display: swap;
    unicode-range: U+000-5FF;
    src: local('Poppins-Regular'), url('../fonts/Poppins-Regular.ttf') format('truetype');
}

@font-face {
    font-family: 'Poppins-Medium';
    font-weight: normal;
    font-style: normal;
    font-display: swap;
    unicode-range: U+000-5FF;
    src: local('Poppins-Medium'), url('../fonts/Poppins-Medium.ttf') format('truetype');
}

@font-face {
    font-family: 'Poppins-SemiBold';
    font-weight: normal;
    font-style: normal;
    font-display: swap;
    unicode-range: U+000-5FF;
    src: local('Poppins-SemiBold'), url('../fonts/Poppins-SemiBold.ttf') format('truetype');
}

@font-face {
    font-family: 'Poppins-Bold';
    font-weight: normal;
    font-style: normal;
    font-display: swap;
    unicode-range: U+000-5FF;
    src: local('Poppins-Bold'), url('../fonts/Poppins-Bold.ttf') format('truetype');
}

@font-face {
    font-family: 'SourceSans3-Regular';
    font-weight: normal;
    font-style: normal;
    font-display: swap;
    unicode-range: U+000-5FF;
    src: local('SourceSans3-Regular'), url('../fonts/SourceSans3-Regular.ttf') format('truetype');
}

.fw-300 {
    font-family: "Poppins-Light";
}

.fw-400 {
    font-family: "Poppins-Regular";
}

.fw-500 {
    font-family: "Poppins-Medium";
}

.fw-600 {   
    font-family: "Poppins-SemiBold";
}

.fw-700 {
    font-family: "Poppins-Bold";
}

.fs-12 {
    font-size: var(--font-size-12);
}

.fs-14 {
    font-size: var(--font-size-14);
}

.fs-16 {
    font-size: var(--font-size-16);
}

.fs-18 {
    font-size: var(--font-size-18);
}

.fs-24 {
    font-size: var(--font-size-24);
}

.uppercase {
    text-transform: uppercase;
} 