<?php include_once '../header.php'; ?>

<script src="js/script-v1.js"></script>

<?php if (isset($_GET['finalizar'])) { ?>
	<script>
		$( window ).on("load", function() {
    		if (localStorage.getItem('RESERVA_APP_HORARIO') == null) {
    			window.location.href = "../";
    		} else {
    			finalizarReserva();
    		}
    	});
	</script>
<?php } elseif (isset($_GET['q'])) { ?>
	<script>
		$( window ).on("load", function() {
			findDadosReserva(<?php echo $_GET['q']; ?>);
    	});
    </script>
<?php } ?>

<div class="mt-4">
	<div id="msg-erro" class="text-danger mb-3"></div>
	
    <h5 class="msg-finalizado m-auto mb-4"> Agendamento finalizado! </h5>
	
	<div class="w-100 text-center mt-3">
		<img src="" id="imageQrCode" width="125" height="125" alt="QRCode">
	</div>
	
	<h6 class="msg-alerta m-auto mt-3 mb-3"> IMPORTANTE </h6>
	
	<div class="text-center fw-600 mb-3 fs-6">
        Faça um print dessa tela e leve junto com você no dia de sua visita.
	</div>
	
	<div class="text-center mb-3 fs-6">
    	Há 15 minutos de tolerância caso tenha algum atraso do horário reservado.
	</div>
	
	<div class="text-center mb-3 fs-6">
    	O tempo de permanência na refeição é de 2 horas.
    </div>
    
    <div class="text-center mb-5 fs-6">
    	<span class="fw-600">Sugestão de rota:</span> Se você estiver na BR-101 indicamos que venha através da Cidade
    	de Tubarão em direção ao município Gravatal, Armazém até chegar em São Martinho e assim pegar a Estrada Geral
    	Vargem do Cedro onde irá percorrer 12km sendo 7km de estrada de chão.
    </div>
	
	<h6 class="text-center mb-3"> DADOS DA RESERVA </h6>
	
	<div>
        <label class="fs-6"> Número reserva: </label>
    	<label id="codigo" class="semibold"> </label>
	</div>
	
	<div>
        <label class="fs-6"> Tipo: </label>
    	<label id="tipo" class="semibold"> </label>
	</div>

	<div>
        <label class="fs-6"> Número pessoas: </label>
    	<label id="quantidade" class="semibold"> </label>
	</div>
	
	<div>
        <label class="fs-6"> Data: </label>
    	<label id="data" class="semibold"> </label>
	</div>

	<div>
        <label class="fs-6"> Horário: </label>
    	<label id="horario" class="semibold"> </label>
	</div>

	<div>
        <label class="fs-6"> Nome: </label>
    	<label id="nome" class="semibold"> </label>
	</div>
	
	<div>
        <label class="fs-6"> Celular: </label>
    	<label id="telefone" class="semibold"> </label>
	</div>

	<div>
        <label class="fs-6"> E-mail: </label>
    	<label id="email" class="semibold"> </label>
	</div>
	
</div>

<?php include_once '../footer.php'; ?>