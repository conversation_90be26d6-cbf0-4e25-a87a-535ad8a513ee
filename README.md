### Fluss Haus - Agendamento online

#### Sobre

Projeto para acesso dos clientes da Fluss Haus para realizar agendamento online de reservas no restaurante.

#### Tecnologias utilizadas

* Tecnologias: PHP (Versão 8.2.10)
* Banco de dados: MariaDB (Versão 10.5.23)
* Estrutura/modelo/framework: Bootstrap - JQuery - Orientado a Objeto

#### Servidor e repositório do projeto

* Servidor: Servidor HSYS
* Tipo servidor: VPS
* Provedor do servidor: Hostinger
* Servidor aplicação: Apache
* Backup banco de dados: Diário no horário de 12:30 e 00:30 (com monitoramento + double check semanal)
* Backup arquivos: Diário no horário de 12:30 e 00:30 (com monitoramento + double check semanal)
* Repositório projeto: Bitbucket (bitbucket.org)
* E-mail conta repositório: <EMAIL>
* Nome projeto no repositório: flusshaus-reserva
* Tanto o sistema quanto o banco de dados estão implantados no servidor VPS da Hostinger (conta Hostinger: <EMAIL>)
* O suporte e manutenção do servidor é realizada pela _HSYS - Desenvolvimento de Sistemas_ e conciliada com a _IT Tecnologia (Irineu Teza)_, ao qual a HSYS mantém um contrato de prestação de serviço para suporte e manutenção ao servidor

#### Funcionalidades

* Tela de visualização e seleção de calendário e escolha do produto
* Definição de quantidade de pessoas adultas e crianças
* Seleção de horários disponíveis conforme quantidade de pessoas definidas para a visita
* Identificação e cadastro de novo cliente
* Finalização da reserva com QRCode e orientações gerais

#### Ambiente de produção e acesso

Link de acesso: https://reservas.flusshaus.com.br

#### Data de início do projeto

21 de agosto de 2021.

#### Autores

HSYS - Desenvolvimento de Sistemas
(hsys.dev.br)

#### Propriedade

Este projeto tem como proprietário a HSYS DESENVOLVIMENTO DE SISTEMAS LTDA, CNPJ 35.726.192/0001-00.
