<?php

require_once '../../config/ProjectStage.php';
require_once '../../config/Auth.php';

$auth = new Auth();
$resultAuth =  $auth->askAuth();
$endpoint = ProjectStage::endpoint() . "/horario-agendamento/" . $_POST['atrativo'] . "/" . $_POST['data'] . "/" . $_POST['quantidade'];
$curl = curl_init();

curl_setopt($curl, CURLOPT_URL, $endpoint);
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
curl_setopt($curl, CURLOPT_HTTPHEADER, array('Authorization: Bearer ' . $resultAuth->token));
curl_setopt($curl, CURLOPT_HTTPGET, 1);

$response_body = curl_exec($curl);
curl_close($curl);

foreach (json_decode($response_body) as $horario) {
?>

	<div class="col-4 d-grid gap-2 mb-3">
		<div class="btn-group-vertical" id="group-button-<?php echo $horario->idHorario; ?>">

			<?php if ($horario->esgotado) { ?>

				<label class="btn btn-lotado p-0">
					<div class="btn-horario-1"> <?php echo substr($horario->horario, 0, 5); ?> </div>
					<div class="btn-horario-2"> Lotado </div>
				</label>

			<?php } else { ?>
				<input type="radio" name="horario" value="<?php echo $horario->idHorario; ?>" id="danger-outlined-<?php echo $horario->idHorario; ?>" class="btn-check" required="required">

				<label class="btn btn-horario p-0" for="danger-outlined-<?php echo $horario->idHorario; ?>" onclick="selecionarHorario('<?php echo $horario->idHorario; ?>')">
					<div class="btn-horario-1"> <?php echo substr($horario->horario, 0, 5); ?> </div>
					<div class="btn-horario-2"> 
					<span class="fw-600"><?php echo $horario->disponivel; ?></span> 
					<span class="fw-400"><?php echo ($horario->disponivel == 1 ? 'disponível' : 'disponíveis'); ?></span>
				</div>
				</label>

			<?php } ?>
		</div>
	</div>

<?php } ?>